<?php

namespace App\Exceptions;

use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\HttpKernel\Exception\MethodNotAllowedHttpException;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;
use App\Services\Response\ResponseService;
use Spatie\Permission\Exceptions\PermissionDoesNotExist;
use Spatie\Permission\Exceptions\RoleDoesNotExist;
use Spatie\Permission\Exceptions\UnauthorizedException;
use Throwable;

class Handler extends ExceptionHandler
{
    /**
     * A list of the exception types that are not reported.
     *
     * @var array<int, class-string<Throwable>>
     */
    protected $dontReport = [
        //
    ];

    /**
     * A list of the inputs that are never flashed for validation exceptions.
     *
     * @var array<int, string>
     */
    protected $dontFlash = [
        'current_password',
        'password',
        'password_confirmation',
    ];

    /**
     * Register the exception handling callbacks for the application.
     *
     * @return void
     */
    public function register()
    {
        $this->reportable(function (Throwable $e) {
            // 记录异常
            Log::error("系统异常 - 异常类型: " . get_class($e) . ", 错误信息: {$e->getMessage()}, 文件: {$e->getFile()}, 行号: {$e->getLine()}");
        });
    }

    /**
     * 自定义异常渲染
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Throwable  $exception
     * @return \Symfony\Component\HttpFoundation\Response
     *
     * @throws \Throwable
     */
    public function render($request, Throwable $exception)
    {
        // 处理所有API请求的异常，确保返回统一的响应格式
        if ($request->expectsJson() || $request->is('api/*')) {
            // 记录异常，便于调试
            if (config('app.debug')) {
                Log::debug("API异常调试 - 异常类型: " . get_class($exception) . ", 错误信息: {$exception->getMessage()}, 文件: {$exception->getFile()}, 行号: {$exception->getLine()}");

                // 对于授权相关异常，记录更详细的信息
                if (
                    $exception instanceof AuthorizationException ||
                    $exception instanceof AccessDeniedHttpException ||
                    $exception instanceof UnauthorizedException
                ) {
                    Log::debug("授权异常详情 - 异常类型: " . get_class($exception) . ", 堆栈跟踪: {$exception->getTraceAsString()}");
                }
            }

            return $this->handleApiException($request, $exception);
        }

        return parent::render($request, $exception);
    }

    /**
     * 处理API请求的异常
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Throwable  $exception
     * @return \Symfony\Component\HttpFoundation\Response
     */
    protected function handleApiException($request, Throwable $exception)
    {
        // 处理 HttpResponseException (如认证失败等)
        if ($exception instanceof HttpResponseException) {
            return $exception->getResponse();
        }

        if ($exception instanceof ModelNotFoundException) {
            return $this->handleModelNotFoundException($exception);
        }

        if ($exception instanceof NotFoundHttpException) {
            return $this->handleNotFoundHttpException();
        }

        if ($exception instanceof MethodNotAllowedHttpException) {
            return $this->handleMethodNotAllowedHttpException();
        }

        // 处理授权异常 (FormRequest 中的 authorize 方法返回 false)
        if ($exception instanceof AuthorizationException) {
            return $this->handleAuthorizationException($exception);
        }

        // 处理访问被拒绝异常
        if ($exception instanceof AccessDeniedHttpException) {
            return $this->handleAccessDeniedHttpException($exception);
        }

        if ($exception instanceof HttpException) {
            return $this->handleHttpException($exception);
        }

        if ($exception instanceof ValidationException) {
            return $this->handleValidationException($exception);
        }

        // 处理 Spatie 权限异常
        if ($exception instanceof PermissionDoesNotExist) {
            return $this->handlePermissionDoesNotExist($exception);
        }

        if ($exception instanceof RoleDoesNotExist) {
            return $this->handleRoleDoesNotExist($exception);
        }

        if ($exception instanceof UnauthorizedException) {
            return $this->handleUnauthorizedException($exception);
        }

        return $this->handleGenericException($request, $exception);
    }

    /**
     * 处理模型未找到异常
     *
     * @param  \Illuminate\Database\Eloquent\ModelNotFoundException  $exception
     * @return \Illuminate\Http\JsonResponse
     */
    protected function handleModelNotFoundException(ModelNotFoundException $exception)
    {
        $modelName = basename(str_replace('\\', '/', $exception->getModel()));

        // 根据模型名称返回不同的错误消息
        $transKey = 'response.' . strtolower($modelName) . '_not_found';
        $message = Lang::has($transKey)
            ? trans($transKey)
            : trans('response.resource_not_found', ['resource' => trans('response.resource')]);

        return ResponseService::notFound($message);
    }

    /**
     * 处理路由未找到异常
     *
     * @return \Illuminate\Http\JsonResponse
     */
    protected function handleNotFoundHttpException()
    {
        return ResponseService::notFound(
            trans('response.route_not_found')
        );
    }

    /**
     * 处理方法不允许异常
     *
     * @return \Illuminate\Http\JsonResponse
     */
    protected function handleMethodNotAllowedHttpException()
    {
        return ResponseService::error(
            trans('response.method_not_allowed'),
            405,
            null,
            405
        );
    }

    /**
     * 处理HTTP异常
     *
     * @param  \Symfony\Component\HttpKernel\Exception\HttpException  $exception
     * @return \Illuminate\Http\JsonResponse
     */
    protected function handleHttpException(HttpException $exception)
    {
        return ResponseService::error(
            $exception->getMessage() ?: trans('response.http_error'),
            $exception->getStatusCode(),
            null,
            $exception->getStatusCode()
        );
    }

    /**
     * 处理验证异常
     *
     * @param  \Illuminate\Validation\ValidationException  $exception
     * @return \Illuminate\Http\JsonResponse
     */
    protected function handleValidationException(ValidationException $exception)
    {
        $errors = $exception->validator->errors()->toArray();
        $firstErrorField = array_key_first($errors);
        $firstErrorMessage = $errors[$firstErrorField][0] ?? trans('validation.failed');

        return ResponseService::validationError($firstErrorMessage, null);
    }

    /**
     * 处理通用异常
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Throwable  $exception
     * @return \Symfony\Component\HttpFoundation\Response
     */
    protected function handleGenericException($request, Throwable $exception)
    {
        // 在API请求中，即使在调试模式下也不返回详细的异常信息
        // 这样可以确保API响应格式的一致性和安全性
        if ($request->expectsJson() || $request->is('api/*')) {
            // 记录异常以便调试
            if (config('app.debug')) {
                Log::error("API异常调试 - 异常类型: " . get_class($exception) . ", 错误信息: {$exception->getMessage()}, 文件: {$exception->getFile()}, 行号: {$exception->getLine()}");
            }

            // 返回标准格式的错误响应
            return ResponseService::serverError(
                trans('response.server_error')
            );
        }

        // 非API请求，遵循Laravel默认行为
        if (config('app.debug')) {
            // 在调试模式下返回详细错误信息
            return parent::render($request, $exception);
        }

        // 生产环境下返回通用错误消息
        return ResponseService::serverError(
            trans('response.server_error')
        );
    }

    /**
     * 处理权限不存在异常
     *
     * @param  \Spatie\Permission\Exceptions\PermissionDoesNotExist  $exception
     * @return \Illuminate\Http\JsonResponse
     */
    protected function handlePermissionDoesNotExist(PermissionDoesNotExist $exception)
    {
        return ResponseService::error(
            trans('permission.permission_not_found', ['name' => $exception->getMessage()]),
            403,
            null,
            403
        );
    }

    /**
     * 处理角色不存在异常
     *
     * @param  \Spatie\Permission\Exceptions\RoleDoesNotExist  $exception
     * @return \Illuminate\Http\JsonResponse
     */
    protected function handleRoleDoesNotExist(RoleDoesNotExist $exception)
    {
        return ResponseService::error(
            trans('permission.role_not_found', ['name' => $exception->getMessage()]),
            403,
            null,
            403
        );
    }

    /**
     * 处理未授权异常
     *
     * @param  \Spatie\Permission\Exceptions\UnauthorizedException  $exception
     * @return \Illuminate\Http\JsonResponse
     */
    protected function handleUnauthorizedException(UnauthorizedException $e)
    {
        // 记录具体的未授权原因
        Log::warning("未授权访问 - 错误信息: {$e->getMessage()}, 文件: {$e->getFile()}, 行号: {$e->getLine()}");

        return ResponseService::forbidden(
            trans('response.forbidden')
        );
    }

    /**
     * 处理访问被拒绝异常
     *
     * @param  \Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException  $exception
     * @return \Illuminate\Http\JsonResponse
     */
    protected function handleAccessDeniedHttpException(AccessDeniedHttpException $e)
    {
        // 记录具体的访问被拒绝原因
        Log::warning("访问被拒绝 - 异常类型: AccessDeniedHttpException, 错误信息: {$e->getMessage()}, 文件: {$e->getFile()}, 行号: {$e->getLine()}");

        return ResponseService::forbidden(
            trans('response.forbidden')
        );
    }

    /**
     * 处理授权异常 (FormRequest 中的 authorize 方法返回 false)
     *
     * @param  \Illuminate\Auth\Access\AuthorizationException  $exception
     * @return \Illuminate\Http\JsonResponse
     */
    protected function handleAuthorizationException(AuthorizationException $e)
    {
        // 记录具体的授权失败原因
        Log::warning("授权失败 - 异常类型: AuthorizationException, 错误信息: {$e->getMessage()}, 文件: {$e->getFile()}, 行号: {$e->getLine()}");

        return ResponseService::forbidden(
            trans('response.forbidden')
        );
    }
}
