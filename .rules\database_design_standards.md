---
description: Laravel数据库设计规范
globs: "**/*.sql"
alwaysApply: true
---
---
# Rule: Laravel数据库设计规范 (Laravel Database Design Standards)
# Description: 定义Laravel项目中数据库设计的标准，包括表结构、字段命名、索引设计等。
# Trigger: 当设计新的数据库表或修改现有表结构时。
# AppliesTo: 数据库迁移文件、SQL文件等。
---

## 概述

本规范旨在确保Laravel项目中的数据库设计遵循一致的标准，提高数据库的可读性、性能和可维护性。良好的数据库设计是应用程序性能和可扩展性的基础。

## 核心原则

1. **命名一致性**: 使用一致的命名约定
2. **字段类型适当**: 为每个字段选择适当的数据类型
3. **索引合理**: 合理设计索引，提高查询性能
4. **注释完善**: 为表和字段添加清晰的注释
5. **关系明确**: 明确定义表之间的关系

## 表设计规范

### 表命名

1. **使用复数形式**: 表名应使用英文复数形式（如`users`而非`user`）
2. **全小写**: 表名应全部使用小写字母
3. **下划线分隔**: 多个单词之间使用下划线分隔（如`user_profiles`）
4. **前缀使用**: 如果需要，可以使用前缀区分不同模块的表（如`auth_permissions`）

### 表注释

每个表都应有明确的注释，说明表的用途：

```sql
CREATE TABLE users (
    -- 字段定义
) COMMENT = '用户表，存储系统用户基本信息';
```

### 常见表结构

#### 主表

```sql
CREATE TABLE users (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '自增主键ID',
    -- 业务字段
    created_at TIMESTAMP NULL COMMENT '创建时间',
    updated_at TIMESTAMP NULL COMMENT '更新时间',
    -- 可选的软删除字段
    -- deleted_at TIMESTAMP NULL COMMENT '删除时间'
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户表';
```

#### 关联表

```sql
CREATE TABLE role_permissions (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '自增主键ID',
    role_id BIGINT UNSIGNED NOT NULL COMMENT '角色ID',
    permission_id BIGINT UNSIGNED NOT NULL COMMENT '权限ID',
    created_at TIMESTAMP NULL COMMENT '创建时间',
    updated_at TIMESTAMP NULL COMMENT '更新时间',
    UNIQUE KEY role_permission_unique (role_id, permission_id),
    FOREIGN KEY (role_id) REFERENCES roles (id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES permissions (id) ON DELETE CASCADE
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '角色权限关联表';
```

## 字段设计规范

### 字段命名

1. **蛇形命名法**: 字段名使用蛇形命名法（如`first_name`）
2. **全小写**: 字段名应全部使用小写字母
3. **避免缩写**: 除非是广泛接受的缩写，否则应使用完整单词
4. **避免保留字**: 避免使用SQL保留字作为字段名

### 常见字段命名

- **主键**: `id`
- **外键**: `{table_singular}_id`（如`user_id`、`role_id`）
- **创建时间**: `created_at`
- **更新时间**: `updated_at`
- **删除时间**: `deleted_at`（用于软删除）
- **状态字段**: `status`、`is_active`、`is_deleted`等
- **排序字段**: `sort_order`、`position`等
- **名称字段**: `name`、`title`、`label`等
- **描述字段**: `description`、`content`、`remark`等

### 字段类型选择

1. **整数类型**:
   - `TINYINT`: 小范围整数（如状态、标志）
   - `INT`: 中等范围整数
   - `BIGINT`: 大范围整数（如ID、计数）

2. **字符串类型**:
   - `VARCHAR`: 可变长度字符串（如名称、标题）
   - `TEXT`: 长文本（如描述、内容）
   - `CHAR`: 固定长度字符串（如代码、缩写）

3. **日期时间类型**:
   - `DATE`: 日期
   - `TIME`: 时间
   - `DATETIME`/`TIMESTAMP`: 日期和时间

4. **布尔类型**:
   - `BOOLEAN`/`TINYINT(1)`: 布尔值（如状态标志）

5. **小数类型**:
   - `DECIMAL`: 精确小数（如金额、价格）
   - `FLOAT`/`DOUBLE`: 近似小数（如非精确计算）

### 字段注释

每个字段都应有明确的注释，说明字段的用途：

```sql
username VARCHAR(255) NOT NULL COMMENT '用户名，唯一标识符',
```

### 默认值

为字段设置合理的默认值：

```sql
is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT '账户是否激活',
```

## 索引设计规范

### 主键索引

每个表都应有一个主键，通常是自增整数：

```sql
id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '自增主键ID',
```

### 唯一索引

对于需要唯一性约束的字段，应添加唯一索引：

```sql
username VARCHAR(255) NOT NULL COMMENT '用户名，唯一标识符',
UNIQUE KEY username_unique (username),
```

### 外键索引

对于外键字段，应添加索引以提高连接查询性能：

```sql
user_id BIGINT UNSIGNED NOT NULL COMMENT '用户ID',
INDEX user_id_index (user_id),
```

### 复合索引

对于经常一起查询的多个字段，应添加复合索引：

```sql
INDEX name_status_index (name, status),
```

## 关系设计规范

### 一对一关系

使用外键和唯一约束实现一对一关系：

```sql
-- 用户表
CREATE TABLE users (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '自增主键ID',
    -- 其他字段
) COMMENT = '用户表';

-- 用户配置表
CREATE TABLE user_settings (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '自增主键ID',
    user_id BIGINT UNSIGNED NOT NULL COMMENT '用户ID',
    -- 其他字段
    UNIQUE KEY user_id_unique (user_id),
    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
) COMMENT = '用户配置表';
```

### 一对多关系

使用外键实现一对多关系：

```sql
-- 用户表
CREATE TABLE users (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '自增主键ID',
    -- 其他字段
) COMMENT = '用户表';

-- 文章表
CREATE TABLE articles (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '自增主键ID',
    user_id BIGINT UNSIGNED NOT NULL COMMENT '作者ID',
    -- 其他字段
    INDEX user_id_index (user_id),
    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
) COMMENT = '文章表';
```

### 多对多关系

使用中间表实现多对多关系：

```sql
-- 用户表
CREATE TABLE users (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '自增主键ID',
    -- 其他字段
) COMMENT = '用户表';

-- 角色表
CREATE TABLE roles (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '自增主键ID',
    -- 其他字段
) COMMENT = '角色表';

-- 用户角色关联表
CREATE TABLE user_roles (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '自增主键ID',
    user_id BIGINT UNSIGNED NOT NULL COMMENT '用户ID',
    role_id BIGINT UNSIGNED NOT NULL COMMENT '角色ID',
    created_at TIMESTAMP NULL COMMENT '创建时间',
    updated_at TIMESTAMP NULL COMMENT '更新时间',
    UNIQUE KEY user_role_unique (user_id, role_id),
    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
    FOREIGN KEY (role_id) REFERENCES roles (id) ON DELETE CASCADE
) COMMENT = '用户角色关联表';
```

## SQL文件组织规范

### 文件位置

SQL文件应放在`database/sql/`目录下，按功能模块组织：

```
database/
└── sql/
    └── {module}/                  # 模块SQL文件目录
        ├── create_{table}_table.sql
        └── alter_{table}_table.sql
```

### 文件命名

- 创建表: `create_{table}_table.sql`
- 修改表: `alter_{table}_table.sql`
- 插入数据: `insert_{table}_data.sql`
- 创建视图: `create_{view_name}_view.sql`

### 文件内容

SQL文件应包含清晰的注释，说明文件的用途和执行条件：

```sql
-- --------------------------------------------------------
-- 表结构: `users`
-- 描述: 存储系统用户基本信息
-- --------------------------------------------------------

CREATE TABLE IF NOT EXISTS users (
    -- 字段定义
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户表';
```

## 最佳实践

1. **使用InnoDB引擎**: 默认使用InnoDB存储引擎，支持事务和外键
2. **使用UTF8MB4字符集**: 使用utf8mb4字符集和utf8mb4_unicode_ci排序规则，支持完整的Unicode字符集
3. **合理使用外键**: 在需要保证数据一致性的场景使用外键约束
4. **避免过度范式化**: 在性能要求高的场景，适当反范式化可以提高查询性能
5. **使用软删除**: 对于重要数据，使用软删除而非物理删除

## 触发条件与适用范围

- **触发时机**:
  - 当设计新的数据库表时
  - 当修改现有表结构时
  - 当编写数据库迁移文件时
  - 当编写SQL脚本时

- **适用范围**:
  - 数据库迁移文件
  - SQL文件
  - 数据库设计文档
