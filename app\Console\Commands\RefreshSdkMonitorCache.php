<?php

/**
 * SDK监控缓存刷新命令
 * @desc 定时刷新SDK监控数据缓存，预热常用的查询数据
 * <AUTHOR> chen<PERSON><PERSON><EMAIL>
 * @date 2025-01-20
 */

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\SdkMonitorService;
use App\Services\Cache\SdkMonitorCacheService;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class RefreshSdkMonitorCache extends Command
{
    /**
     * 命令名称和签名
     *
     * @var string
     */
    protected $signature = 'sdk-monitor:refresh-cache
                            {--app-ids= : 指定要预热的应用ID列表，用逗号分隔，默认使用配置文件中的列表}';

    /**
     * 命令描述
     *
     * @var string
     */
    protected $description = '刷新SDK监控数据缓存，预热今天的数据';

    /**
     * 缓存服务实例
     *
     * @var SdkMonitorCacheService
     */
    private $cacheService;

    /**
     * 创建命令实例
     */
    public function __construct()
    {
        parent::__construct();
        $this->cacheService = new SdkMonitorCacheService();
    }

    /**
     * 执行命令
     *
     * @return int
     */
    public function handle()
    {
        $this->info('开始刷新SDK监控缓存...');

        try {
            // 获取要预热的应用ID列表
            $appIds = $this->getAppIds();

            // 获取今天的时间范围
            $timeRange = $this->getTodayTimeRange();

            $successCount = 0;
            $totalCount = 0;

            foreach ($appIds as $appId) {
                $totalCount++;

                if ($this->warmupCacheForApp($appId, $timeRange['start'], $timeRange['end'])) {
                    $successCount++;
                }
            }

            $this->info("缓存刷新完成！成功: {$successCount}/{$totalCount}");

            Log::info("SDK监控缓存刷新完成 - 成功数量: {$successCount}/{$totalCount}, 应用ID列表: " . implode(',', $appIds));

            return Command::SUCCESS;
        } catch (\Exception $e) {
            $this->error('缓存刷新失败: ' . $e->getMessage());

            Log::error("SDK监控缓存刷新失败 - 错误信息: {$e->getMessage()}, 文件: {$e->getFile()}, 行号: {$e->getLine()}, 堆栈跟踪: {$e->getTraceAsString()}");

            return Command::FAILURE;
        }
    }

    /**
     * 获取要预热的应用ID列表
     *
     * @return array
     */
    private function getAppIds(): array
    {
        $appIdsOption = $this->option('app-ids');

        if ($appIdsOption) {
            // 从命令行参数获取
            return array_map('intval', explode(',', $appIdsOption));
        }

        // 从配置文件获取，如果没有配置则使用默认值
        return config('cache.sdk_monitor.warmup_app_ids', [1]);
    }

    /**
     * 获取今天的时间范围
     *
     * @return array
     */
    private function getTodayTimeRange(): array
    {
        $now = Carbon::now();

        return [
            'start' => $now->copy()->startOfDay()->format('Y-m-d H:i:s'),
            'end' => $now->copy()->endOfDay()->format('Y-m-d H:i:s')
        ];
    }

    /**
     * 为指定应用和时间范围预热缓存
     *
     * @param int $appId 应用ID
     * @param string $startTime 开始时间
     * @param string $endTime 结束时间
     * @return bool 是否成功
     */
    private function warmupCacheForApp(int $appId, string $startTime, string $endTime): bool
    {
        try {
            $this->line("预热应用 {$appId} 的缓存: {$startTime} ~ {$endTime}");

            // 使用缓存服务的预热方法
            $success = $this->cacheService->warmupCache(
                $appId,
                $startTime,
                $endTime,
                function () use ($appId, $startTime, $endTime) {
                    // 创建服务实例并获取数据
                    $service = new SdkMonitorService($appId, $startTime, $endTime);
                    return $service->fetchDashboardDataFromDatabase();
                }
            );

            if ($success) {
                $this->info("✓ 应用 {$appId} 缓存预热成功");
            } else {
                $this->warn("✗ 应用 {$appId} 缓存预热失败");
            }

            return $success;
        } catch (\Exception $e) {
            $this->error("✗ 应用 {$appId} 缓存预热异常: " . $e->getMessage());

            Log::error("SDK监控缓存预热异常，应用ID: {$appId}，时间范围: {$startTime} ~ {$endTime}，错误: {$e->getMessage()}");

            return false;
        }
    }
}
