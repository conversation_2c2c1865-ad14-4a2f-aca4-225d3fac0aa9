<?php

/**
 * 响应服务
 * @desc 提供统一的API响应格式，包含code、message和data结构。
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2025-05-15
 * @todo 考虑将错误码和消息配置化
 */

namespace App\Services\Response;

use Illuminate\Http\JsonResponse;

class ResponseService
{
    /**
     * 创建成功响应
     *
     * @param mixed $data 响应数据
     * @param string $message 响应消息
     * @param int $code 响应码
     * @param int $httpStatusCode HTTP状态码
     * @return JsonResponse
     */
    public static function success($data = null, ?string $message = null, int $code = 0, int $httpStatusCode = 200): JsonResponse
    {
        // 如果没有提供消息，使用翻译的默认成功消息
        if ($message === null) {
            $message = trans('response.success');
        }

        return new JsonResponse([
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ], $httpStatusCode);
    }

    /**
     * 创建错误响应
     *
     * @param string $message 错误消息
     * @param int $code 错误码
     * @param mixed $data 错误数据
     * @param int $httpStatusCode HTTP状态码
     * @return JsonResponse
     */
    public static function error(?string $message = null, int $code = 400, $data = null, int $httpStatusCode = 400): JsonResponse
    {
        // 如果没有提供消息，使用翻译的默认错误消息
        if ($message === null) {
            $message = trans('response.error');
        }

        return new JsonResponse([
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ], $httpStatusCode);
    }

    /**
     * 创建验证失败响应
     *
     * @param string $message 验证错误消息
     * @param mixed $data 验证错误数据
     * @return JsonResponse
     */
    public static function validationError(?string $message = null, $data = null): JsonResponse
    {
        $httpStatusCode = JsonResponse::HTTP_UNPROCESSABLE_ENTITY; // 422

        // 如果没有提供消息，使用翻译的默认验证错误消息
        if ($message === null) {
            $message = trans('validation.failed');
        }

        return new JsonResponse([
            'code'    => $httpStatusCode,
            'message' => $message,
            'data'    => $data,
        ], $httpStatusCode);
    }

    /**
     * 创建未授权响应
     *
     * @param string $message 未授权消息
     * @return JsonResponse
     */
    public static function unauthorized(?string $message = null): JsonResponse
    {
        $httpStatusCode = JsonResponse::HTTP_UNAUTHORIZED; // 401

        // 如果没有提供消息，使用翻译的默认未授权消息
        if ($message === null) {
            $message = trans('response.unauthorized');
        }

        return new JsonResponse([
            'code'    => $httpStatusCode,
            'message' => $message,
            'data'    => null,
        ], $httpStatusCode);
    }

    /**
     * 创建禁止访问响应
     *
     * @param string $message 禁止访问消息
     * @return JsonResponse
     */
    public static function forbidden(?string $message = null): JsonResponse
    {
        $httpStatusCode = JsonResponse::HTTP_FORBIDDEN; // 403

        // 如果没有提供消息，使用翻译的默认禁止访问消息
        if ($message === null) {
            $message = trans('response.forbidden');
        }

        return new JsonResponse([
            'code'    => $httpStatusCode,
            'message' => $message,
            'data'    => null,
        ], $httpStatusCode);
    }

    /**
     * 创建资源不存在响应
     *
     * @param string $message 资源不存在消息
     * @return JsonResponse
     */
    public static function notFound(?string $message = null): JsonResponse
    {
        $httpStatusCode = JsonResponse::HTTP_NOT_FOUND; // 404

        // 如果没有提供消息，使用翻译的默认资源不存在消息
        if ($message === null) {
            $message = trans('response.not_found');
        }

        return new JsonResponse([
            'code'    => $httpStatusCode,
            'message' => $message,
            'data'    => null,
        ], $httpStatusCode);
    }

    /**
     * 创建服务器错误响应
     *
     * @param string $message 服务器错误消息
     * @return JsonResponse
     */
    public static function serverError(?string $message = null): JsonResponse
    {
        $httpStatusCode = JsonResponse::HTTP_INTERNAL_SERVER_ERROR; // 500

        // 如果没有提供消息，使用翻译的默认服务器错误消息
        if ($message === null) {
            $message = trans('response.server_error');
        }

        return new JsonResponse([
            'code'    => $httpStatusCode,
            'message' => $message,
            'data'    => null,
        ], $httpStatusCode);
    }
}
