<?php

/**
 * 自定义认证中间件
 * @desc 扩展Laravel的认证中间件，使用自定义响应格式处理未认证请求
 * <AUTHOR> chen<PERSON><PERSON><PERSON><EMAIL>
 * @date 2025-05-16
 * @todo 暂无特定待办事项。
 */

namespace App\Http\Middleware;

use App\Services\Response\ResponseService;
use Illuminate\Auth\Middleware\Authenticate as Middleware;
use Illuminate\Http\Request;

class CustomAuthenticate extends Middleware
{
    /**
     * 获取用户未认证时应重定向的路径
     *
     * @param  Request  $request
     * @return string|null
     */
    protected function redirectTo($request)
    {
        if (! $request->expectsJson()) {
            return route('login');
        }
    }

    /**
     * 处理未认证的请求
     *
     * @param  Request  $request
     * @param  array  $guards
     * @return void
     *
     * @throws \Illuminate\Auth\AuthenticationException
     */
    protected function unauthenticated($request, array $guards)
    {
        if ($request->expectsJson()) {
            // 直接抛出响应而不是使用 abort
            throw new \Illuminate\Http\Exceptions\HttpResponseException(
                ResponseService::unauthorized()
            );
        }

        parent::unauthenticated($request, $guards);
    }
}
