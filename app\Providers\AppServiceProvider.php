<?php

/**
 * 应用服务提供者
 * @desc 注册应用程序的服务和绑定。
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2025-05-15
 * @todo 无
 */

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use App\Services\Auth\ThirdPartyAuthServiceInterface;
use App\Services\Auth\ThirdPartyAuthService;
use App\Services\Response\ResponseService;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        // 注册响应服务为单例
        $this->app->singleton(ResponseService::class, function () {
            return new ResponseService();
        });
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        //
    }
}
