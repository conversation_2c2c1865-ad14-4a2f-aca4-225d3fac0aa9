<?php

/**
 * 获取监控数据服务类
 *
 * 整合了监控数据获取、崩溃排行和错误排行功能的综合服务类，
 * 提供应用监控数据的统计分析功能，包括崩溃率、错误率等关键指标
 *
 * @desc 整合了监控数据获取、崩溃排行和错误排行功能的综合服务类
 * <AUTHOR> ch<PERSON><PERSON><PERSON><EMAIL>
 * @date 2025/05/28
 * @todo 添加缓存机制优化查询性能，增加数据导出功能
 * @package App\Services\HitBug
 */

namespace App\Services\HitBug;

use App\Models\HitBug\ExceptionRecordNew;
use App\Models\HitBug\ExceptionStreamAll;
use App\Models\HitBug\VersionWhiteList;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class GetMonitorData
{
    /**
     * 默认查询天数偏移量
     */
    const DEFAULT_DAYS_OFFSET = 15;

    /**
     * 默认返回结果数量限制
     */
    const DEFAULT_RESULT_LIMIT = 7;

    /**
     * 异常事件类型常量
     */
    const EVENT_CRASH = 'exception_crash';
    const EVENT_ERROR = 'exception_error';
    const EVENT_START = 'exception_start';

    /**
     * 版本字段类型常量
     */
    const VERSION_TYPE_INNER = 'inner_version';
    const VERSION_TYPE_APP = 'app_version';

    /**
     * 默认统计值常量
     */
    const DEFAULT_RATE_VALUE = '0.00';
    const DEFAULT_COUNT_VALUE = 0;
    const DEFAULT_DEVICE_COUNT = '0';

    /**
     * 开始日期
     *
     * @var string
     */
    protected string $startDate;

    /**
     * 结束日期
     *
     * @var string
     */
    protected string $endDate;

    /**
     * 效能后台ID
     *
     * @var int
     */
    protected int $developerAppId;

    /**
     * 构造函数
     *
     * 初始化监控数据查询的基本参数
     *
     * @param string $startDate 开始日期，格式：Y-m-d H:i:s
     * @param string $endDate 结束日期，格式：Y-m-d H:i:s
     * @param int $developerAppId 效能后台应用ID
     */
    public function __construct(string $startDate, string $endDate, int $developerAppId)
    {
        $this->startDate = $startDate;
        $this->endDate = $endDate;
        $this->developerAppId = $developerAppId;
    }

    /**
     * 获取监控数据
     *
     * 获取应用的监控数据，包括崩溃错误率统计
     * 注意：图表数据需要通过 getChartDataByInnerVersion() 或 getChartDataByAppVersion() 方法单独获取
     *
     * @return array 包含crashErrorRate的数组
     */
    public function getData(): array
    {
        $startData = $this->getStartData();
        $crashData = $this->getExceptionData(self::EVENT_CRASH);
        $errorData = $this->getExceptionData(self::EVENT_ERROR);

        $startCount = $startData['num'] ?? self::DEFAULT_COUNT_VALUE;
        $crashCount = $crashData['num'] ?? self::DEFAULT_COUNT_VALUE;
        $errorCount = $errorData['num'] ?? self::DEFAULT_COUNT_VALUE;

        return [
            'crash' => $this->calculateRate($crashCount, $startCount),
            'error' => $this->calculateRate($errorCount, $startCount),
            'start_count' => $startCount,
            'crash_count' => $crashCount,
            'error_count' => $errorCount,
            'start_dev_num' => $startData['device_num'] ?? self::DEFAULT_DEVICE_COUNT,
            'crash_dev_num' => $crashData['device_num'] ?? self::DEFAULT_DEVICE_COUNT,
            'error_dev_num' => $errorData['device_num'] ?? self::DEFAULT_DEVICE_COUNT,
        ];
    }

    /**
     * 按内部版本获取折线图数据
     *
     * 按内部版本统计监控指标，包括崩溃率和错误率
     *
     * @param array $innerVersions 内部版本号数组
     * @return array 按内部版本分组的监控数据，包含crash_rate和error_rate
     */
    public function getChartDataByInnerVersion(array $innerVersions): array
    {
        if (empty($innerVersions)) {
            return [];
        }

        return $this->getChartDataByVersion(self::VERSION_TYPE_INNER, $innerVersions);
    }

    /**
     * 按应用版本获取折线图数据
     *
     * 按应用版本统计监控指标，包括崩溃率和错误率
     *
     * @param array $appVersions 应用版本号数组
     * @return array 按应用版本分组的监控数据，包含crash_rate和error_rate
     */
    public function getChartDataByAppVersion(array $appVersions): array
    {
        if (empty($appVersions)) {
            return [];
        }

        return $this->getChartDataByVersion(self::VERSION_TYPE_APP, $appVersions);
    }

    /**
     * 通用的按版本获取折线图数据
     *
     * @param string $versionField 版本字段名
     * @param array $versions 版本号数组
     * @return array 按版本分组的监控数据
     */
    private function getChartDataByVersion(string $versionField, array $versions): array
    {
        $extendedDates = $this->getExtendedDateRange();
        $rawData = $this->getExceptionStatisticsByVersionField($extendedDates, $versionField, $versions);
        $groupedData = $this->groupDataByVersionField($rawData, $versionField);
        $calculatedData = $this->calculateRates($groupedData);

        return $this->sortAndLimitResults($calculatedData);
    }

    /**
     * 获取扩展的日期范围
     *
     * 将开始时间向前扩展指定天数，以获取更多历史数据
     *
     * @return array 扩展后的日期范围
     */
    protected function getExtendedDateRange(): array
    {
        $extendedStartDate = Carbon::parse($this->startDate)
            ->subDays(self::DEFAULT_DAYS_OFFSET)
            ->toDateTimeString();

        return [$extendedStartDate, $this->endDate];
    }

    /**
     * 通用的按版本字段获取异常统计数据
     *
     * @param array $dates 日期范围
     * @param string $versionField 版本字段名
     * @param array $versions 版本号数组
     * @return array 异常统计数据
     */
    private function getExceptionStatisticsByVersionField(array $dates, string $versionField, array $versions): array
    {
        $eventTypes = [self::EVENT_CRASH, self::EVENT_ERROR, self::EVENT_START];

        return ExceptionStreamAll::query()
            ->selectRaw("event_name, {$versionField}, count(1) as count, count(distinct server_dev_str) as dev_str_count")
            ->whereIn('event_name', $eventTypes)
            ->whereBetween("stream_time", [strtotime($dates[0]), strtotime($dates[1])])
            ->whereBetween("stream_date", [
                Carbon::parse($dates[0])->toDateString(),
                Carbon::parse($dates[1])->toDateString()
            ])
            ->where("extra_app_id", $this->developerAppId)
            ->where("is_emulator", 0) // 过滤模拟器数据
            ->where($versionField, '!=', '') // 版本号不为空
            ->whereIn($versionField, $versions)
            ->groupBy("event_name", $versionField)
            ->getFromSR();
    }

    /**
     * 通用的按版本字段分组数据
     *
     * @param array $rawData 原始统计数据
     * @param string $versionField 版本字段名
     * @return array 按版本分组的数据
     */
    private function groupDataByVersionField(array $rawData, string $versionField): array
    {
        $groupedData = [];

        foreach ($rawData as $item) {
            $version = $item[$versionField];
            $eventName = $item['event_name'];
            $groupedData[$version][$eventName] = $item;
        }

        return $groupedData;
    }

    /**
     * 计算崩溃率和错误率
     *
     * 基于启动次数计算崩溃率和错误率
     *
     * @param array $groupedData 按版本分组的数据
     * @return array 包含计算结果的数据
     */
    protected function calculateRates(array $groupedData): array
    {
        $result = [];

        foreach ($groupedData as $version => $eventData) {
            $startCount = $this->getEventCount($eventData, self::EVENT_START, 1);
            $crashCount = $this->getEventCount($eventData, self::EVENT_CRASH);
            $errorCount = $this->getEventCount($eventData, self::EVENT_ERROR);

            $result[$version] = [
                'crash_rate' => $this->calculateRate($crashCount, $startCount),
                'error_rate' => $this->calculateRate($errorCount, $startCount),
            ];
        }

        return $result;
    }

    /**
     * 获取事件次数
     *
     * 从事件数据中获取指定事件的次数，支持默认值
     *
     * @param array $eventData 事件数据
     * @param string $eventName 事件名称
     * @param int $defaultValue 默认值
     * @return int 事件次数
     */
    private function getEventCount(array $eventData, string $eventName, int $defaultValue = self::DEFAULT_COUNT_VALUE): int
    {
        return $eventData[$eventName]['count'] ?? $defaultValue;
    }

    /**
     * 计算比率
     *
     * 计算异常事件相对于启动次数的比率，以百分比形式返回
     *
     * @param int $eventCount 事件次数
     * @param int $startCount 启动次数
     * @return string 格式化的比率（保留2位小数）
     */
    protected function calculateRate(int $eventCount, int $startCount): string
    {
        if ($startCount <= 0) {
            return self::DEFAULT_RATE_VALUE;
        }

        $rate = ($eventCount / $startCount) * 100;
        return bcadd(round($rate, 2), 0, 2);
    }

    /**
     * 排序并限制结果数量
     *
     * 对结果按版本号进行数值排序（倒序），并限制返回数量
     *
     * @param array $data 计算后的数据
     * @return array 排序并限制后的结果
     */
    protected function sortAndLimitResults(array $data): array
    {
        // 简单排序
        uksort($data, function ($a, $b) {
            $versionA = str_replace('.', '', $a);
            $versionB = str_replace('.', '', $b);
            $numA = is_numeric($versionA) ? (float)$versionA : 0;
            $numB = is_numeric($versionB) ? (float)$versionB : 0;
            return $numB <=> $numA; // 倒序排序
        });

        return array_slice($data, 0, self::DEFAULT_RESULT_LIMIT, true);
    }

    /**
     * 获取异常数据统计
     *
     * 根据事件类型获取异常数据统计，包含白名单过滤逻辑
     *
     * @param string $eventName 事件名称
     * @return array 包含num和device_num的统计数据
     */
    protected function getExceptionData(string $eventName): array
    {
        $dateRange = $this->getDateRange();
        $whitelists = $this->getWhitelists($eventName);

        $query = $this->buildExceptionQuery($eventName, $dateRange);
        $this->applyWhitelistFilters($query, $whitelists['app_version'], $whitelists['exception_block_id']);
        $this->applyKeywordFilter($query, $eventName, $dateRange[0]);

        $result = $query->firstFromSR();

        return $result ?: ['num' => self::DEFAULT_COUNT_VALUE, 'device_num' => self::DEFAULT_DEVICE_COUNT];
    }

    /**
     * 获取日期范围
     *
     * @return array [开始日期, 结束日期]
     */
    private function getDateRange(): array
    {
        return [
            Carbon::parse($this->startDate)->toDateString(),
            Carbon::parse($this->endDate)->toDateString()
        ];
    }

    /**
     * 获取白名单数据
     *
     * @param string $eventName 事件名称
     * @return array 包含应用版本和异常ID白名单的数组
     */
    private function getWhitelists(string $eventName): array
    {
        return [
            'app_version' => $this->getAppVersionWhite(),
            'exception_block_id' => $this->getExceptionBlockIdWhite($eventName == 'exception_crash' ? 1 : 2)
        ];
    }

    /**
     * 构建异常查询
     *
     * @param string $eventName 事件名称
     * @param array $dateRange 日期范围
     * @return mixed 查询构建器
     */
    private function buildExceptionQuery(string $eventName, array $dateRange)
    {
        return ExceptionStreamAll::query()
            ->selectRaw('COUNT(1) AS num, COUNT(distinct server_dev_str) AS device_num')
            ->where('extra_app_id', $this->developerAppId)
            ->where('event_name', $eventName)
            ->whereBetween('stream_date', $dateRange);
    }

    /**
     * 应用白名单过滤
     *
     * 对查询应用版本白名单和异常ID白名单过滤
     *
     * @param $query 查询构建器
     * @param array $appVersionWhitelist 应用版本白名单
     * @param array $exceptionBlockIdWhitelist 异常ID白名单
     * @return void
     */
    protected function applyWhitelistFilters($query, array $appVersionWhitelist, array $exceptionBlockIdWhitelist): void
    {
        // 排除版本白名单
        if (!empty($appVersionWhitelist)) {
            $query->whereNotIn('app_version', $appVersionWhitelist);
        }

        // 排除异常ID白名单
        if (!empty($exceptionBlockIdWhitelist)) {
            $upperCaseWhitelist = array_map('strtoupper', $exceptionBlockIdWhitelist);
            $query->whereNotIn(DB::raw('upper(exception_block_id)'), $upperCaseWhitelist);
        }
    }

    /**
     * 应用关键词过滤
     *
     * 根据关键词过滤规则排除特定异常
     *
     * @param $query 查询构建器
     * @param string $eventName 事件名称
     * @param string $startDate 开始日期
     * @return void
     */
    protected function applyKeywordFilter($query, string $eventName, string $startDate): void
    {
        $keywordFilterSql = "
            upper(exception_block_id) not in (
                select upper(a.exception_block_id) as exception_block_id
                from (
                    select extra_app_id, exception_block_id, unnest
                    from exception_stream_keyword, unnest(keywords) AS unnest
                    where array_length(keywords) > 0
                    and event_name = ?
                    and stream_date >= ?
                ) as a
                inner join (
                    select developer_app_id, keyword
                    from exception_filter_keyword
                    where is_stat = 0
                ) as b on a.extra_app_id = b.developer_app_id and a.unnest = b.keyword
                group by a.exception_block_id
            )
        ";

        $query->whereRaw($keywordFilterSql, [$eventName, $startDate]);
    }

    /**
     * 获取启动数据统计
     *
     * 获取应用启动次数统计，用于计算崩溃率和错误率的基数
     *
     * @return array 包含num和device_num的统计数据
     */
    protected function getStartData(): array
    {
        $dateRange = $this->getDateRange();

        $result = ExceptionStreamAll::query()
            ->selectRaw('COUNT(1) AS num, COUNT(distinct server_dev_str) AS device_num')
            ->where('extra_app_id', $this->developerAppId)
            ->where('event_name', self::EVENT_START)
            ->whereBetween('stream_date', $dateRange)
            ->firstFromSR();

        return $result ?: ['num' => self::DEFAULT_COUNT_VALUE, 'device_num' => self::DEFAULT_DEVICE_COUNT];
    }

    /**
     * 获取白名单异常ID列表
     *
     * 根据类型获取已加入白名单的异常ID列表
     *
     * @param int $type 异常类型
     * @return array 异常ID列表
     */
    protected function getExceptionBlockIdWhite(int $type): array
    {
        $list = ExceptionRecordNew::query()
            ->select(['exception_block_id'])
            ->where('type', $type)
            ->where('is_add_white_list', 1)
            ->getFromSR();

        return array_column($list, 'exception_block_id');
    }

    /**
     * 获取应用版本白名单
     *
     * 获取当前应用的版本白名单列表
     *
     * @return array 版本号列表
     */
    protected function getAppVersionWhite(): array
    {
        return VersionWhiteList::query()
            ->where('developer_app_id', $this->developerAppId)
            ->pluck('app_version')
            ->toArray();
    }
}
