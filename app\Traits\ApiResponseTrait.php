<?php

/**
 * API响应特性
 * @desc 提供统一的API响应方法，可被控制器或其他类使用，确保响应格式一致性。
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><EMAIL>
 * @date 2025-05-15
 * @todo 考虑添加更多特定业务场景的响应方法。
 */

namespace App\Traits;

use Illuminate\Http\JsonResponse;
use App\Services\Response\ResponseService;

trait ApiResponseTrait
{
    /**
     * 返回成功响应
     *
     * @param mixed $data 响应数据
     * @param string|null $message 响应消息
     * @param int $code 响应码
     * @param int $httpStatusCode HTTP状态码
     * @return JsonResponse
     */
    protected function respondSuccess($data = null, ?string $message = null, int $code = 0, int $httpStatusCode = 200): JsonResponse
    {
        return ResponseService::success($data, $message, $code, $httpStatusCode);
    }

    /**
     * 返回错误响应
     *
     * @param string|null $message 错误消息
     * @param int $code 错误码
     * @param mixed $data 错误数据
     * @param int $httpStatusCode HTTP状态码
     * @return JsonResponse
     */
    protected function respondError(?string $message = null, int $code = 400, $data = null, int $httpStatusCode = 400): JsonResponse
    {
        return ResponseService::error($message, $code, $data, $httpStatusCode);
    }

    /**
     * 返回验证失败响应
     *
     * @param string|null $message 验证错误消息
     * @param mixed $data 验证错误数据
     * @return JsonResponse
     */
    protected function respondValidationError(?string $message = null, $data = null): JsonResponse
    {
        return ResponseService::validationError($message, $data);
    }

    /**
     * 返回未授权响应
     *
     * @param string|null $message 未授权消息
     * @return JsonResponse
     */
    protected function respondUnauthorized(?string $message = null): JsonResponse
    {
        return ResponseService::unauthorized($message);
    }

    /**
     * 返回禁止访问响应
     *
     * @param string|null $message 禁止访问消息
     * @return JsonResponse
     */
    protected function respondForbidden(?string $message = null): JsonResponse
    {
        return ResponseService::forbidden($message);
    }

    /**
     * 返回资源不存在响应
     *
     * @param string|null $message 资源不存在消息
     * @return JsonResponse
     */
    protected function respondNotFound(?string $message = null): JsonResponse
    {
        return ResponseService::notFound($message);
    }

    /**
     * 返回服务器错误响应
     *
     * @param string|null $message 服务器错误消息
     * @return JsonResponse
     */
    protected function respondServerError(?string $message = null): JsonResponse
    {
        return ResponseService::serverError($message);
    }

    /**
     * 返回创建成功响应
     *
     * @param mixed $data 响应数据
     * @param string|null $message 响应消息
     * @return JsonResponse
     */
    protected function respondCreated($data = null, ?string $message = null): JsonResponse
    {
        return ResponseService::success($data, $message ?? trans('response.created'), 0, 201);
    }

    /**
     * 返回无内容响应
     *
     * @return JsonResponse
     */
    protected function respondNoContent(): JsonResponse
    {
        return ResponseService::success(null, '', 0, 204);
    }
}
