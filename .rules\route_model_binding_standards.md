---
description: Laravel路由模型绑定规范
globs: "**/*.php"
alwaysApply: true
---
---
# Rule: Laravel路由模型绑定规范 (Laravel Route Model Binding Standards)
# Description: 定义Laravel项目中路由模型绑定的最佳实践，避免常见陷阱和错误。
# Trigger: 当开发涉及路由模型绑定的功能时。
# AppliesTo: 控制器、路由定义和请求验证类。
---

## 概述

本规范旨在确保Laravel项目中的路由模型绑定功能正确实现，避免常见的陷阱和错误。路由模型绑定是Laravel的一个强大功能，它允许我们直接在控制器方法中注入模型实例，而不是手动查询数据库。然而，如果不正确实现，可能会导致意外行为，如模型未被正确加载或数据库查询未执行。

## 核心原则

1. **参数名称匹配**: 控制器方法参数名必须与路由参数名匹配
2. **类型提示正确**: 使用正确的模型类型提示
3. **显式绑定**: 对于复杂情况，使用显式路由模型绑定
4. **一致性**: 在整个应用程序中保持一致的命名约定

## 路由模型绑定规范

### 1. 参数名称匹配规则

**关键规则**: 控制器方法参数名必须与路由参数名匹配。

当使用`Route::resource`或`Route::apiResource`时，Laravel会自动生成路由，其中路由参数名是模型名称的单数形式（蛇形命名法）。例如，对于`permission-groups`资源，路由参数名将是`{permission_group}`。

#### 正确示例:

```php
// 路由: /permission-groups/{permission_group}
public function show(PermissionGroup $permissionGroup)
{
    // $permissionGroup 将自动被注入
    return new PermissionGroupResource($permissionGroup);
}
```

#### 错误示例:

```php
// 路由: /permission-groups/{permission_group}
public function show(PermissionGroup $group) // 参数名不匹配
{
    // $group 可能不会被正确注入
    return new PermissionGroupResource($group);
}
```

### 2. 类型提示规则

**关键规则**: 始终使用正确的模型类型提示。

确保控制器方法参数使用正确的模型类型提示，这告诉Laravel应该绑定哪个模型。

#### 正确示例:

```php
public function show(PermissionGroup $permissionGroup)
{
    // 正确的类型提示
}
```

#### 错误示例:

```php
public function show(User $permissionGroup) // 错误的类型提示
{
    // 将尝试绑定User模型而不是PermissionGroup模型
}
```

### 3. 请求验证类中的路由参数访问

在请求验证类中访问路由参数时，使用与路由定义一致的参数名。

#### 正确示例:

```php
// 对应路由: /permission-groups/{permission_group}
public function rules()
{
    $permissionGroup = $this->route('permission_group');
    
    return [
        'name' => ['required', Rule::unique('permission_groups')->ignore($permissionGroup->id)],
    ];
}
```

#### 错误示例:

```php
public function rules()
{
    $group = $this->route('group'); // 错误的参数名
    
    return [
        'name' => ['required', Rule::unique('permission_groups')->ignore($group->id)],
    ];
}
```

### 4. 显式路由模型绑定

对于复杂情况或自定义查询逻辑，使用显式路由模型绑定。

#### 在RouteServiceProvider中:

```php
public function boot()
{
    Route::model('permission_group', \App\Models\Permission\PermissionGroup::class);
    
    // 或使用bind方法进行更复杂的绑定
    Route::bind('permission_group', function ($value) {
        return \App\Models\Permission\PermissionGroup::where('id', $value)
            ->orWhere('name', $value)
            ->firstOrFail();
    });
    
    // ...
}
```

## 实际案例分析

### 案例: 权限组控制器路由模型绑定问题

#### 问题描述:

在`PermissionGroupController`中，即使使用了正确的类型提示`PermissionGroup $group`，系统仍然没有正确执行数据库查询。

#### 根本原因:

控制器方法参数名`$group`与路由参数名`{permission_group}`不匹配。

#### 错误代码:

```php
// 路由: /permission-groups/{permission_group}
public function show(PermissionGroup $group) // 参数名不匹配
{
    $group->load('permissions');
    return new PermissionGroupResource($group);
}
```

#### 修复方案:

```php
// 路由: /permission-groups/{permission_group}
public function show(PermissionGroup $permissionGroup) // 参数名匹配
{
    $permissionGroup->load('permissions');
    return new PermissionGroupResource($permissionGroup);
}
```

## 调试技巧

如果怀疑路由模型绑定不正常工作:

1. **检查路由列表**: 使用`php artisan route:list`查看实际的路由参数名
2. **添加日志**: 在控制器方法中添加日志，记录模型是否正确加载
3. **检查SQL查询**: 使用`DB::enableQueryLog()`和`DB::getQueryLog()`检查是否执行了数据库查询
4. **检查模型的exists属性**: 使用`$model->exists`检查模型是否从数据库加载

## 最佳实践总结

1. **保持一致的命名约定**: 在整个应用程序中使用一致的命名约定
2. **使用资源控制器**: 优先使用Laravel的资源控制器和路由
3. **参数名匹配**: 确保控制器方法参数名与路由参数名匹配
4. **类型提示正确**: 使用正确的模型类型提示
5. **显式绑定**: 对于复杂情况，使用显式路由模型绑定
6. **添加调试日志**: 在怀疑问题时添加调试日志

## 触发条件与适用范围

- **触发时机**:
  - 当开发涉及路由模型绑定的功能时
  - 当修改现有控制器方法时
  - 当进行代码审查时

- **适用范围**:
  - 控制器类
  - 路由定义文件
  - 请求验证类
