---
description: Laravel功能模块开发规范
globs: "**/*.php"
alwaysApply: true
---
---
# Rule: Laravel功能模块开发规范 (Laravel Module Development Standards)
# Description: 定义Laravel项目中功能模块的开发标准，包括目录结构、命名约定、代码组织、API响应格式等。
# Trigger: 当开发新功能模块或修改现有功能模块时。
# AppliesTo: 项目中的所有PHP文件，特别是控制器、请求验证类、资源类、模型等。
---

## 概述

本规范旨在确保Laravel项目中的所有功能模块遵循一致的开发标准，提高代码质量和可维护性。通过统一的目录结构、命名约定、代码组织和API响应格式，使项目更加清晰、易于理解和扩展。

## 核心原则

1. **模块化组织**: 按功能模块组织代码，避免在同一目录下堆积过多文件
2. **统一响应**: 所有API响应使用统一的格式，包含code、message和data字段
3. **国际化支持**: 所有面向用户的消息都应支持国际化
4. **代码复用**: 抽象共用逻辑，避免代码重复
5. **清晰命名**: 使用清晰、一致的命名约定

## 目录结构规范

### 功能模块目录组织

每个功能模块应按以下结构组织：

```
app/
├── Http/
│   ├── Controllers/
│   │   └── {Module}/              # 模块控制器目录
│   │       └── {Module}Controller.php
│   ├── Requests/
│   │   └── {Module}/              # 模块请求验证类目录
│   │       ├── Store{Entity}Request.php
│   │       └── Update{Entity}Request.php
│   └── Resources/
│       └── {Module}/              # 模块资源类目录
│           ├── {Entity}Resource.php
│           └── {Entity}Collection.php
├── Models/
│   └── {Module}/                  # 模块模型目录
│       └── {Entity}.php
├── Services/
│   └── {Module}/                  # 模块服务类目录
│       └── {Module}Service.php
└── Traits/                        # 通用特性目录
    └── {Feature}Trait.php
```

其中：
- `{Module}`: 功能模块名称，如User、Role、Permission等
- `{Entity}`: 实体名称，通常与模块名称相同或相关

### 数据库SQL文件组织

数据库SQL文件应放在`database/sql/`目录下，按功能模块组织：

```
database/
└── sql/
    └── {module}/                  # 模块SQL文件目录
        ├── create_{table}_table.sql
        └── alter_{table}_table.sql
```

## 命名约定

### 目录命名

- 目录名使用单数形式的PascalCase（如`User`而非`Users`）
- 基础类目录使用`Base`命名（如`Requests/Base`）

### 文件命名

- 控制器：`{Entity}Controller.php`
- 请求验证类：`Store{Entity}Request.php`、`Update{Entity}Request.php`
- 资源类：`{Entity}Resource.php`、`{Entity}Collection.php`
- 模型：`{Entity}.php`
- 服务类：`{Entity}Service.php`、`{Entity}ServiceInterface.php`

### 类命名

- 控制器：`{Entity}Controller`
- 请求验证类：`Store{Entity}Request`、`Update{Entity}Request`
- 资源类：`{Entity}Resource`、`{Entity}Collection`
- 模型：`{Entity}`
- 服务类：`{Entity}Service`、`{Entity}ServiceInterface`

## 代码组织规范

### 控制器规范

1. **继承基础控制器**：所有控制器应继承`App\Http\Controllers\Controller`
2. **使用资源控制器**：优先使用Laravel的资源控制器方法（index, store, show, update, destroy）
3. **使用请求验证类**：使用专门的请求验证类处理输入验证
4. **使用资源类**：使用资源类格式化响应数据
5. **使用服务类**：复杂业务逻辑应封装在服务类中
6. **使用统一响应格式**：使用`ApiResponseTrait`提供的方法返回统一格式的响应

### 请求验证类规范

1. **继承基础请求类**：所有请求验证类应继承`App\Http\Requests\Base\BaseFormRequest`
2. **实现授权逻辑**：在`authorize()`方法中实现授权逻辑
3. **实现验证规则**：在`rules()`方法中定义验证规则
4. **使用国际化消息**：验证错误消息应支持国际化

### 资源类规范

1. **继承基础资源类**：单个资源应继承`App\Http\Resources\Base\BaseJsonResource`
2. **继承基础集合类**：集合资源应继承`App\Http\Resources\Base\ListResourceCollection`
3. **指定集合资源类型**：在集合类中指定`$collects`属性
4. **格式化日期时间**：使用`format('Y-m-d H:i:s')`格式化日期时间
5. **布尔值类型转换**：确保布尔值字段正确转换为布尔类型

### 模型规范

1. **指定数据库连接**：使用`protected $connection = 'new_package_tool';`指定数据库连接
2. **定义可填充字段**：使用`$fillable`属性定义可批量赋值的字段
3. **定义日期字段**：使用`$dates`属性定义日期字段
4. **定义关联关系**：使用方法定义模型之间的关联关系
5. **使用工厂类**：为模型创建工厂类，便于测试

## API响应规范

### 统一响应格式

所有API响应应使用以下统一格式：

```json
{
    "code": 0,           // 业务状态码，0表示成功，其他值表示错误
    "message": "操作成功", // 响应消息，支持国际化
    "data": {            // 响应数据，可以是对象、数组或null
        // 具体数据
    }
}
```

### 列表响应格式

列表类API响应应使用以下格式：

```json
{
    "code": 0,
    "message": "操作成功",
    "data": {
        "list": [        // 数据列表，而非data.data嵌套
            // 列表项
        ],
        "meta": {        // 分页元数据，不包含links和path
            "current_page": 1,
            "from": 1,
            "last_page": 5,
            "per_page": 15,
            "to": 15,
            "total": 75
        }
    }
}
```

### 错误响应格式

错误响应应使用以下格式：

```json
{
    "code": 400,         // HTTP状态码或自定义错误码
    "message": "错误消息", // 错误描述，应使用国际化
    "data": null         // 通常为null，特殊情况可包含错误详情
}
```

### 验证错误响应格式

验证错误响应应使用以下格式：

```json
{
    "code": 422,
    "message": "验证失败：用户名已被占用", // 第一条验证错误消息
    "data": null                      // 不返回详细的验证错误信息
}
```

## 国际化规范

1. **默认语言**：项目默认语言为中文（zh_CN）
2. **翻译文件组织**：按功能模块组织翻译文件
3. **使用翻译函数**：使用`trans()`函数获取翻译文本
4. **响应消息翻译**：所有API响应消息都应使用翻译文本

## 数据库规范

1. **表名**：使用复数形式，全小写，下划线分隔（如`users`、`role_permissions`）
2. **主键**：使用自增整数`id`作为主键
3. **外键命名**：使用`{table_singular}_id`格式（如`user_id`、`role_id`）
4. **时间戳字段**：包含`created_at`和`updated_at`字段
5. **SQL文件注释**：使用`COMMENT`为表和字段添加注释
6. **字段注释**：所有字段都应有明确的注释说明其用途

## 文档规范

1. **API文档**：为每个功能模块创建API文档，放在`doc/{module}`目录下
2. **Postman集合**：创建Postman测试集合，便于API测试
3. **使用说明**：创建模块使用说明文档，说明功能和使用方法

## 测试规范

1. **单元测试**：为模型和服务类编写单元测试
2. **功能测试**：为控制器编写功能测试
3. **测试覆盖率**：关键功能应有足够的测试覆盖率

## 安全规范

1. **输入验证**：所有用户输入都应经过验证
2. **授权检查**：所有操作都应检查用户权限
3. **防止SQL注入**：使用查询构建器或预处理语句
4. **防止XSS攻击**：对输出进行适当转义

## 性能规范

1. **查询优化**：避免N+1查询问题
2. **索引使用**：为常用查询字段添加索引
3. **缓存使用**：适当使用缓存提高性能

## 代码质量规范

1. **代码注释**：所有类和方法都应有适当的注释
2. **代码格式**：遵循PSR-2/PSR-12代码风格规范
3. **命名规范**：使用有意义的名称，避免缩写
4. **方法长度**：控制方法长度，避免过长方法
5. **依赖注入**：使用依赖注入而非直接实例化

## 示例实现

参考用户管理模块的实现：

- 控制器：`app/Http/Controllers/User/UserController.php`
- 请求验证类：`app/Http/Requests/User/StoreUserRequest.php`
- 资源类：`app/Http/Resources/User/UserResource.php`
- 模型：`app/Models/User/User.php`
- SQL文件：`database/sql/create_users_table.sql`
- 文档：`doc/用户管理模块.md`

## 触发条件与适用范围

- **触发时机**：
  - 当开发新功能模块时
  - 当修改现有功能模块时
  - 当进行代码审查时

- **适用范围**：
  - 所有PHP文件，特别是控制器、请求验证类、资源类、模型等
  - 数据库SQL文件
  - API文档和使用说明
