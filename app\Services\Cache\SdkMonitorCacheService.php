<?php

/**
 * SDK监控缓存服务类
 * @desc 处理SDK监控数据的缓存逻辑，包括缓存键生成、数据存储和获取
 * <AUTHOR> chen<PERSON><PERSON><EMAIL>
 * @date 2025-01-20
 */

namespace App\Services\Cache;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class SdkMonitorCacheService
{
    /**
     * 缓存键前缀
     */
    const CACHE_PREFIX = 'sdk_monitor';

    /**
     * 缓存时间（分钟）
     */
    const CACHE_DURATION = 10;

    /**
     * 生成缓存键
     *
     * 基于请求参数生成唯一的缓存键
     *
     * @param int $developerAppId 开发者应用ID
     * @param string $startTime 开始时间
     * @param string $endTime 结束时间
     * @return string 缓存键
     */
    public function generateCacheKey(int $developerAppId, string $startTime, string $endTime): string
    {
        // 生成参数字符串
        $params = "{$developerAppId}|{$startTime}|{$endTime}";

        // 生成MD5哈希
        $hash = md5($params);

        return self::CACHE_PREFIX . ":{$developerAppId}:{$hash}";
    }

    /**
     * 获取缓存数据
     *
     * 根据参数获取缓存的仪表板数据
     *
     * @param int $developerAppId 开发者应用ID
     * @param string $startTime 开始时间
     * @param string $endTime 结束时间
     * @return array|null 缓存的数据，如果不存在返回null
     */
    public function getCachedData(int $developerAppId, string $startTime, string $endTime): ?array
    {
        try {
            $cacheKey = $this->generateCacheKey($developerAppId, $startTime, $endTime);

            $data = Cache::get($cacheKey);

            if ($data !== null) {
                Log::info("SDK监控缓存命中 - 缓存键: {$cacheKey}, 应用ID: {$developerAppId}, 开始时间: {$startTime}, 结束时间: {$endTime}");
            }

            return $data;
        } catch (\Exception $e) {
            Log::error("获取SDK监控缓存失败 - 错误: {$e->getMessage()}, 应用ID: {$developerAppId}, 开始时间: {$startTime}, 结束时间: {$endTime}");

            return null;
        }
    }

    /**
     * 存储缓存数据
     *
     * 将仪表板数据存储到缓存中
     *
     * @param int $developerAppId 开发者应用ID
     * @param string $startTime 开始时间
     * @param string $endTime 结束时间
     * @param array $data 要缓存的数据
     * @return bool 是否存储成功
     */
    public function setCachedData(int $developerAppId, string $startTime, string $endTime, array $data): bool
    {
        try {
            $cacheKey = $this->generateCacheKey($developerAppId, $startTime, $endTime);

            $success = Cache::put($cacheKey, $data, now()->addMinutes(self::CACHE_DURATION));

            if ($success) {
                Log::info("SDK监控数据已缓存 - 缓存键: {$cacheKey}, 应用ID: {$developerAppId}, 开始时间: {$startTime}, 结束时间: {$endTime}, 缓存时长: " . self::CACHE_DURATION . "分钟");
            }

            return $success;
        } catch (\Exception $e) {
            Log::error("存储SDK监控缓存失败 - 错误: {$e->getMessage()}, 应用ID: {$developerAppId}, 开始时间: {$startTime}, 结束时间: {$endTime}");

            return false;
        }
    }

    /**
     * 预热缓存数据
     *
     * 为指定的应用和时间范围预热缓存
     *
     * @param int $developerAppId 开发者应用ID
     * @param string $startTime 开始时间
     * @param string $endTime 结束时间
     * @param callable $dataProvider 数据提供者回调函数
     * @return bool 是否预热成功
     */
    public function warmupCache(int $developerAppId, string $startTime, string $endTime, callable $dataProvider): bool
    {
        try {
            // 获取新数据
            $data = $dataProvider();

            // 存储到缓存
            $success = $this->setCachedData($developerAppId, $startTime, $endTime, $data);

            if ($success) {
                Log::info("SDK监控缓存预热成功 - 应用ID: {$developerAppId}, 开始时间: {$startTime}, 结束时间: {$endTime}");
            }

            return $success;
        } catch (\Exception $e) {
            Log::error("SDK监控缓存预热失败 - 错误: {$e->getMessage()}, 应用ID: {$developerAppId}, 开始时间: {$startTime}, 结束时间: {$endTime}");

            return false;
        }
    }
}
