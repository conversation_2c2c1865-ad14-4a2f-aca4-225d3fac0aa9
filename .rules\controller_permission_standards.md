---
description: Laravel控制器权限校验规范
globs: "**/*.php"
alwaysApply: true
---
---
# Rule: Laravel控制器权限校验规范 (Laravel Controller Permission Standards)
# Description: 定义Laravel项目中基于控制器构造函数的权限校验方案，确保权限校验的一致性、可维护性和可扩展性。
# Trigger: 当开发新的控制器或修改现有控制器的权限校验逻辑时。
# AppliesTo: 控制器类和相关的请求验证类。
---

## 概述

本规范定义了项目中基于控制器构造函数的权限校验方案，确保权限校验的一致性、可维护性和可扩展性。项目采用 Spatie/laravel-permission 包进行权限管理，通过特性(Trait)和基础控制器类实现，提供了灵活、可复用的权限校验机制。

## 核心原则

1. **集中管理**: 在控制器层面集中管理权限校验，而非分散在各个方法中
2. **代码复用**: 通过特性和基础控制器类，避免在每个控制器中重复编写权限校验代码
3. **灵活配置**: 支持为不同控制器方法配置不同的权限
4. **自动推断**: 从控制器名称自动推断权限前缀，减少配置工作
5. **超级管理员**: 支持超级管理员角色，自动拥有所有权限

## 权限命名规范

### 基本规则

权限命名必须遵循 `{模块}.{操作}` 的格式，例如：

- `user.list` - 查看用户列表的权限
- `role.create` - 创建角色的权限
- `menu.update` - 更新菜单的权限

### 标准操作命名

标准资源操作的权限命名应遵循以下规范：

| 操作     | 权限后缀 | 示例         | 说明                 |
|----------|----------|--------------|----------------------|
| 列表查询 | list     | user.list    | 查询资源列表的权限   |
| 详情查看 | view     | user.view    | 查看资源详情的权限   |
| 创建     | create   | user.create  | 创建新资源的权限     |
| 更新     | update   | user.update  | 更新现有资源的权限   |
| 删除     | delete   | user.delete  | 删除资源的权限       |

### 非标准操作命名

非标准操作的权限命名应尽量使用简洁、明确的动词，例如：

- `user.import` - 导入用户的权限
- `user.export` - 导出用户的权限
- `role.assign` - 分配角色的权限

## 实现方案

### 核心组件

#### AuthorizesPermissions 特性

`AuthorizesPermissions` 特性是权限校验方案的核心，封装了权限校验逻辑，提供以下功能：

- 在控制器初始化时自动注册权限中间件
- 支持为资源控制器的标准方法配置默认权限
- 支持为非标准方法配置额外权限
- 支持权限关系处理（OR关系和AND关系）
- 支持从控制器名称自动推断权限前缀
- 支持超级管理员角色，自动拥有所有权限

#### ResourceController 基础控制器类

`ResourceController` 是一个抽象基类，继承自 `Controller` 并使用 `AuthorizesPermissions` 特性，为资源控制器提供了默认的权限配置。

### 控制器实现规范

所有需要权限校验的控制器应继承 `ResourceController` 或直接使用 `AuthorizesPermissions` 特性，并遵循以下规范：

1. **权限前缀**：通过 `$permissionPrefix` 属性设置权限前缀，应与模块名称保持一致
2. **资源方法权限**：通过 `$resourcePermissions` 属性配置标准资源方法的权限
3. **额外方法权限**：通过 `$additionalPermissions` 属性配置非标准方法的权限
4. **权限关系**：通过 `$requireAllPermissions` 属性配置权限关系（OR或AND）
5. **排除方法**：通过 `$exceptMethods` 属性配置不需要权限校验的方法
6. **超级管理员**：通过 `$superAdminRole` 属性配置超级管理员角色名称

### 代码示例

```php
class UserController extends ResourceController
{
    /**
     * 权限前缀
     *
     * @var string
     */
    protected $permissionPrefix = 'user';
    
    /**
     * 额外方法的权限映射
     *
     * @var array<string, string|array>
     */
    protected $additionalPermissions = [
        'import' => 'import',
        'export' => 'export',
        'resetPassword' => 'update',
    ];
    
    /**
     * 需要跳过权限检查的方法
     *
     * @var array<int, string>
     */
    protected $exceptMethods = [
        'profile',
    ];
}
```

## 权限校验位置

### 控制器层面校验

权限校验应在控制器层面进行，而非在 Request 类中。Request 类的 `authorize()` 方法应始终返回 `true`：

```php
/**
 * 确定用户是否有权提出此请求
 * 
 * 权限校验已移至控制器层面，此处直接返回 true
 *
 * @return bool
 */
public function authorize()
{
    return true;
}
```

## 超级管理员

### 定义

超级管理员是拥有系统中所有权限的特殊角色，无需进行具体的权限检查。

### 配置

默认情况下，拥有 `super-admin` 角色的用户将自动拥有所有权限。可以通过设置 `$superAdminRole` 属性来自定义超级管理员角色名称：

```php
/**
 * 超级管理员角色名称
 *
 * @var string
 */
protected $superAdminRole = 'administrator';
```

## 权限关系

### OR 关系（默认）

默认情况下，当一个方法需要多个权限时，用户只需满足其中任一权限即可（OR关系）：

```php
protected $additionalPermissions = [
    'manageUsers' => ['user.create', 'user.update'],
];

// 用户拥有 user.create 或 user.update 权限即可访问 manageUsers 方法
```

### AND 关系

如果需要用户同时满足所有权限（AND关系），可以设置 `$requireAllPermissions` 属性：

```php
/**
 * 是否要求同时满足所有权限
 *
 * @var bool
 */
protected $requireAllPermissions = true;

protected $additionalPermissions = [
    'importUsers' => ['user.create', 'user.import'],
];

// 用户必须同时拥有 user.create 和 user.import 权限才能访问 importUsers 方法
```

## 自定义权限检查

对于需要复杂权限逻辑的方法，应将其排除在自动权限检查之外，然后在方法中实现自定义的权限检查：

```php
public function updateProfile(Request $request, User $user)
{
    // 自定义权限检查逻辑
    if (auth()->id() !== $user->id && !auth()->user()->hasPermissionTo('user.admin', 'web')) {
        return $this->respondForbidden('您没有权限更新此用户的资料');
    }
    
    // 处理更新逻辑...
}
```

## 最佳实践

### 权限粒度

权限粒度应根据业务需求合理设置，既不过细也不过粗：

- **过细**：导致权限数量过多，难以管理
- **过粗**：无法满足精细化的权限控制需求

### 权限一致性

确保相似功能使用一致的权限命名，避免权限重复或冲突。

### 权限文档

维护清晰的权限文档，说明每个权限的用途和影响范围。

### 权限测试

编写测试用例验证权限校验功能，确保权限配置正确。

### 默认拒绝

未明确配置权限的方法应默认拒绝访问，避免因遗漏权限配置导致安全漏洞。

## 注意事项

### 性能考虑

权限校验会对性能产生一定影响，应考虑使用 Spatie/laravel-permission 包提供的权限缓存功能，提高性能。

### 调试难度

权限配置分散在各个控制器中，可能增加调试难度，建议维护清晰的文档。

### 权限变更

当业务需求变更导致权限变更时，应同时更新相关控制器的权限配置和权限文档。

## 触发条件与适用范围

- **触发时机**:
  - 当开发新的控制器时
  - 当修改现有控制器的权限校验逻辑时
  - 当进行代码审查时

- **适用范围**:
  - 控制器类
  - 请求验证类
  - 权限相关的中间件和服务类
