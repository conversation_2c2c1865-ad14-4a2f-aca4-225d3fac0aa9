<?php

return [
    /*
    |--------------------------------------------------------------------------
    | 响应消息语言行
    |--------------------------------------------------------------------------
    |
    | 以下语言行用于API响应消息。您可以根据应用程序的需求自由修改这些语言行。
    |
    */

    'success' => '操作成功',
    'error' => '操作失败',

    // 认证相关
    'unauthorized' => '未授权访问',
    'user_disabled' => '用户账户已禁用',
    'invalid_token' => '无效的第三方令牌',
    'logged_out' => '已成功登出',

    // 资源相关
    'not_found' => '资源未找到',
    'resource_not_found' => ':resource未找到',
    'resource' => '资源',
    'route_not_found' => '请求的路由不存在',
    'method_not_allowed' => '请求方法不允许',
    'http_error' => 'HTTP请求错误',
    'user_not_found' => '用户不存在',
    'forbidden' => '禁止访问',

    // 服务器相关
    'server_error' => '服务器错误',
];
