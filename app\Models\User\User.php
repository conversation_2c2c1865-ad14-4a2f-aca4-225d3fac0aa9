<?php

/**
 * 用户模型，定义了用户表的结构、属性及认证相关配置。
 * 该模型用于第三方认证系统集成，不存储用户密码，通过第三方token获取用户信息。
 * 集成了角色和权限管理功能。
 *
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2025/05/15
 */

namespace App\Models\User;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;
use Spatie\Permission\Traits\HasRoles;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable, HasRoles;

    /**
     * 数据库连接名称
     *
     * @var string
     */
    protected $connection = 'mysql';

    /**
     * 可批量赋值的属性
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'username',    // 用户名，从第三方Token获取，唯一业务主键
        'nickname',    // 用户昵称/显示名称
        'is_active',   // 账户是否激活状态
        'third_party_user_id', // 第三方系统中的用户唯一ID
        'last_login_at',       // 最后登录时间
    ];

    /**
     * 序列化时应该隐藏的属性
     *
     * @var array<int, string>
     */
    protected $hidden = [];  // 不需要隐藏任何属性，因为不存储敏感信息如密码

    /**
     * 应该被转换的属性
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_active' => 'boolean',     // 将is_active转换为布尔值
        'last_login_at' => 'datetime:Y-m-d H:i:s', // 将last_login_at转换为指定格式的日期时间
        'created_at' => 'datetime:Y-m-d H:i:s',   // 创建时间，指定格式
        'updated_at' => 'datetime:Y-m-d H:i:s',   // 更新时间，指定格式
    ];
}
