<?php

/**
 * 对应starRocks数仓中exception_stream_all表
 * @desc 对应starRocks数仓中exception_stream_all表
 * <AUTHOR> chen<PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2023/11/16
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Models\HitBug;

use App\Models\StarRocks\BaseModel;

class ExceptionStreamAll extends BaseModel
{
    public const TABLE_NAME = 'exception_stream_all';

    protected $table = self::TABLE_NAME;

    protected $starRocksConnection = 'starRocks_hitbug_database';
}
