<?php

/**
 * starRocks的基础Builder，主要是开发代码的时候有提示
 * @desc starRocks的基础Builder，主要是开发代码的时候有提示
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2023/10/24
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Models\StarRocks;

use Illuminate\Database\Query\Builder;

/**
 * @method static array getSql()
 * @method static array getFromSR()
 * @method static array firstFromSR()
 * @method static void createFromSR(array $data)
 * @method static void deleteFromSR()
 */
class BaseBuilder extends Builder {}
