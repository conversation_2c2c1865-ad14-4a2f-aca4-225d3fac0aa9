---
description: Laravel日志记录规范
globs: "**/*.php"
alwaysApply: true
---
---
# Rule: Laravel日志记录规范 (Laravel Logging Standards)
# Description: 定义Laravel项目中日志记录的标准格式和内容要求，确保日志的一致性和可读性。
# Trigger: 当添加日志记录或修改现有日志时。
# AppliesTo: 项目中的所有PHP文件。
---

## 概述

本规范旨在确保Laravel项目中的所有日志记录都有清晰、一致的格式，提高日志的可读性和可维护性。统一的日志格式有助于问题排查、性能监控和系统维护。

## 核心原则

1. **一行字符串**: 所有日志信息使用一行字符串格式，避免使用数组格式
2. **清晰性**: 日志信息应清晰描述操作和状态
3. **一致性**: 日志格式应保持一致
4. **完整性**: 包含必要的上下文信息
5. **可读性**: 日志应易于阅读和搜索

## 日志格式规范

### 基本格式

所有日志记录必须使用以下一行字符串格式：

```php
Log::info("操作描述 - 关键信息: {$value1}, 其他信息: {$value2}");
```

### 字段分隔符

使用以下分隔符组织日志信息：
- 主要描述与详细信息之间使用 ` - ` (空格-空格) 分隔
- 多个信息项之间使用 `, ` (逗号空格) 分隔
- 键值对使用 `: ` (冒号空格) 分隔

## 日志级别规范

### Info级别

用于记录正常的业务操作和状态信息：

```php
// 缓存操作
Log::info("SDK监控缓存命中 - 缓存键: {$cacheKey}, 应用ID: {$developerAppId}, 开始时间: {$startTime}, 结束时间: {$endTime}");

// 数据操作
Log::info("用户创建成功 - 用户ID: {$userId}, 用户名: {$username}, 创建时间: {$createdAt}");

// 业务流程
Log::info("订单处理完成 - 订单ID: {$orderId}, 用户ID: {$userId}, 金额: {$amount}, 状态: {$status}");
```

### Error级别

用于记录错误和异常信息：

```php
// 系统错误
Log::error("获取SDK监控缓存失败 - 错误: {$e->getMessage()}, 应用ID: {$developerAppId}, 开始时间: {$startTime}, 结束时间: {$endTime}");

// 业务错误
Log::error("用户登录失败 - 用户名: {$username}, 错误: {$errorMessage}, IP: {$ip}, 时间: {$timestamp}");

// 第三方服务错误
Log::error("第三方API调用失败 - 接口: {$apiUrl}, 错误码: {$errorCode}, 错误信息: {$errorMessage}, 请求ID: {$requestId}");
```

### Warning级别

用于记录警告信息：

```php
// 性能警告
Log::warning("查询执行时间过长 - SQL: {$sql}, 执行时间: {$executionTime}ms, 阈值: {$threshold}ms");

// 业务警告
Log::warning("用户登录异常 - 用户ID: {$userId}, 异常IP: {$ip}, 登录时间: {$loginTime}");
```

### Debug级别

用于记录调试信息（仅在开发环境使用）：

```php
// 调试信息
Log::debug("API请求详情 - 方法: {$method}, URL: {$url}, 参数: {$params}, 响应时间: {$responseTime}ms");
```

## 特定场景规范

### 缓存操作日志

```php
// 缓存命中
Log::info("缓存命中 - 缓存键: {$cacheKey}, 数据类型: {$dataType}, 命中时间: {$hitTime}");

// 缓存设置
Log::info("缓存设置成功 - 缓存键: {$cacheKey}, 数据大小: {$dataSize}, 过期时间: {$expireTime}分钟");

// 缓存失效
Log::info("缓存已失效 - 缓存键: {$cacheKey}, 失效原因: {$reason}, 失效时间: {$expiredAt}");
```

### 数据库操作日志

```php
// 查询操作
Log::info("数据查询完成 - 表名: {$tableName}, 条件: {$conditions}, 结果数量: {$resultCount}, 执行时间: {$executionTime}ms");

// 更新操作
Log::info("数据更新成功 - 表名: {$tableName}, 更新条件: {$conditions}, 影响行数: {$affectedRows}");

// 删除操作
Log::info("数据删除成功 - 表名: {$tableName}, 删除条件: {$conditions}, 删除行数: {$deletedRows}");
```

### API调用日志

```php
// 请求日志
Log::info("API请求开始 - 接口: {$endpoint}, 方法: {$method}, 用户ID: {$userId}, 请求ID: {$requestId}");

// 响应日志
Log::info("API请求完成 - 接口: {$endpoint}, 状态码: {$statusCode}, 响应时间: {$responseTime}ms, 请求ID: {$requestId}");

// 错误日志
Log::error("API请求失败 - 接口: {$endpoint}, 错误码: {$errorCode}, 错误信息: {$errorMessage}, 请求ID: {$requestId}");
```

### 业务流程日志

```php
// 流程开始
Log::info("业务流程开始 - 流程名称: {$processName}, 业务ID: {$businessId}, 用户ID: {$userId}, 开始时间: {$startTime}");

// 流程步骤
Log::info("业务流程步骤完成 - 流程名称: {$processName}, 步骤: {$stepName}, 业务ID: {$businessId}, 结果: {$result}");

// 流程结束
Log::info("业务流程完成 - 流程名称: {$processName}, 业务ID: {$businessId}, 总耗时: {$totalTime}ms, 最终状态: {$finalStatus}");
```

## 变量格式化规范

### 字符串变量

直接使用字符串插值：

```php
Log::info("用户操作 - 用户名: {$username}, 操作类型: {$actionType}");
```

### 数值变量

保持原始格式或根据需要格式化：

```php
Log::info("性能指标 - 响应时间: {$responseTime}ms, 内存使用: {$memoryUsage}MB");
```

### 布尔变量

转换为可读的字符串：

```php
$statusText = $isActive ? '激活' : '禁用';
Log::info("用户状态更新 - 用户ID: {$userId}, 状态: {$statusText}");
```

### 数组变量

转换为JSON字符串或提取关键信息：

```php
$paramsJson = json_encode($params);
Log::info("请求参数 - 接口: {$endpoint}, 参数: {$paramsJson}");

// 或者提取关键信息
Log::info("查询条件 - 关键词: {$params['keyword']}, 页码: {$params['page']}, 每页数量: {$params['per_page']}");
```

## 最佳实践

1. **避免敏感信息**: 不要在日志中记录密码、token等敏感信息
2. **使用有意义的描述**: 日志描述应清晰说明操作的目的和结果
3. **包含关键上下文**: 记录足够的上下文信息以便问题排查
4. **保持格式一致**: 同类型的操作使用相同的日志格式
5. **适当的日志级别**: 根据信息的重要性选择合适的日志级别

## 禁止使用的格式

### 禁止使用数组格式

❌ 错误示例：
```php
Log::info('缓存命中', [
    'cache_key' => $cacheKey,
    'developer_app_id' => $developerAppId,
    'start_time' => $startTime,
    'end_time' => $endTime
]);
```

✅ 正确示例：
```php
Log::info("缓存命中 - 缓存键: {$cacheKey}, 应用ID: {$developerAppId}, 开始时间: {$startTime}, 结束时间: {$endTime}");
```

### 禁止多行日志

❌ 错误示例：
```php
Log::info("缓存操作开始");
Log::info("缓存键: {$cacheKey}");
Log::info("应用ID: {$developerAppId}");
```

✅ 正确示例：
```php
Log::info("缓存操作开始 - 缓存键: {$cacheKey}, 应用ID: {$developerAppId}");
```

## 触发条件与适用范围

- **触发时机**:
  - 当添加新的日志记录时
  - 当修改现有日志格式时
  - 当进行代码审查时

- **适用范围**:
  - 项目中的所有PHP文件
  - 特别是服务类、控制器、中间件等核心代码
