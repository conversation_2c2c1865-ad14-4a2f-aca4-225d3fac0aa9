<?php

/**
 * 调用starRocks查询的模型基础类
 * @desc 调用starRocks查询的模型基础类
 * <AUTHOR> chen<PERSON><PERSON><PERSON><EMAIL>
 * @date 2023/10/24
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Models\StarRocks;

use App\Services\StarRocks\StarRocksService;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

/**
 * @method static BaseBuilder query()
 */
abstract class BaseModel extends Model
{
    /**
     * starRocks服务
     *
     * @var StarRocksService
     */
    protected $starRocks;

    /**
     * starRocks数据库连接
     *
     * @var string
     */
    protected $starRocksConnection;

    /**
     * 构造函数
     *
     * @param array $attributes
     */
    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
        // 初始化starRocks服务
        $this->starRocks = new StarRocksService($this->starRocksConnection);
    }

    /**
     * 获取SQL
     *
     * @param Builder $query
     * @return string
     */
    public function scopeGetSql(Builder $query): string
    {
        return $this->getSqlBindings($query);
    }

    /**
     * 查询全部执行
     *
     * @param Builder $query
     * @return array
     */
    public function scopeGetFromSR(Builder $query): array
    {
        return $this->starRocks->query($this->getSqlBindings($query));
    }

    /**
     * 解析sql
     *
     * @param Builder $query
     * @return string
     */
    protected function getSqlBindings(Builder $query): string
    {
        $queryBindings = $query->getBindings();
        foreach ($queryBindings as $key => $val) {
            if (!is_int($val) && !is_float($val)) {
                $val = "'" . trim($val) . "'";
            }
            $queryBindings[$key] = $val;
        }
        // 因为%使用vsprintf会有问题, 所以需要转义
        $sql = str_replace('%', '%%', $query->toSql());
        // 替换?为%s
        $tmp = str_replace('?', '%s', $sql);
        //返回sql
        return vsprintf($tmp, $queryBindings);
    }

    /**
     * 查询单条执行
     *
     * @param Builder $query
     * @return array
     */
    public function scopeFirstFromSR(Builder $query): array
    {
        $query->limit(1);
        $res = $this->starRocks->query($this->getSqlBindings($query));
        return $res[0] ?? [];
    }

    /**
     * 新增
     *
     * @param Builder $query
     * @param array $data
     * @return void
     */
    public function scopeCreateFromSR(Builder $query, array $data): void
    {
        //获取sql
        $sql = $query->toSql();
        //获取 from 关键字出现的位置，忽略大小写
        $fromPos = stripos($sql, 'from');
        //判断是否找到from关键词
        if ($fromPos !== false) {
            //把from前面的字符串去除，获取到表名
            $table = trim(substr($sql, $fromPos + 4));
            $dateTime = Carbon::now()->toDateTimeString();
            !isset($data['created_at']) && $data['created_at'] = $dateTime;
            $data['updated_at'] = $dateTime;
            $fields = implode(',', array_keys($data));
            $values = '"' . implode('","', array_values($data)) . '"';
            $this->starRocks->execute("insert into {$table} ({$fields}) values ({$values})");
        }
    }

    /**
     * 删除
     *
     * @param Builder $query
     * @return void
     */
    public function scopeDeleteFromSR(Builder $query): void
    {
        $sql = $this->getSqlBindings($query);
        //获取 from 关键字出现的位置，忽略大小写
        $fromPos = stripos($sql, 'from');
        //判断是否找到from关键词
        if ($fromPos !== false) {
            //把from前面的字符串去除，并拼接字符串
            $sql = 'delete ' . trim(substr($sql, $fromPos));
        }
        $this->starRocks->execute($sql);
    }
}
