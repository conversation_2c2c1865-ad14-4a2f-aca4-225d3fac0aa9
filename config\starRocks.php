<?php

/**
 * starRocks数据仓库相关配置
 * @desc starRocks数据仓库相关配置
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2023/11/21
 * @todo 这里是后续需要跟进的功能说明
 */

return [
    //数据仓地址
    'starRocks_host' => env('STAR_ROCKS_HOST', ''),
    //数据仓的用户名
    'starRocks_username' => env('STAR_ROCKS_USERNAME', ''),
    //数据仓的密码
    'starRocks_password' => env('STAR_ROCKS_PASSWORD', ''),
    //数据仓的数据库
    'starRocks_hitbug_database' => env('STAR_ROCKS_HITBUG_DATABASE', 'hitbug'),
    //数据仓的数据库
    'starRocks_perfmate_database' => env('STAR_ROCKS_PERFMATE_DATABASE', 'perfmate'),
    //数据仓的端口
    'starRocks_port' => env('STAR_ROCKS_PORT', 9030),
];
