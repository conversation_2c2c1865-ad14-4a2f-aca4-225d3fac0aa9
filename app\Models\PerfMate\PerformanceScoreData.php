<?php

/**
 * 性能分数数据模型
 * @desc 性能分数数据模型
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2025/05/28
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Models\PerfMate;

use App\Models\StarRocks\BaseModel;

class PerformanceScoreData extends BaseModel
{
    const TABLE_NAME = 'performance_score_data';

    protected $table = self::TABLE_NAME;

    protected $starRocksConnection = 'starRocks_perfmate_database';
}
