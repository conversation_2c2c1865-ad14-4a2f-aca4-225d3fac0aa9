<?php

/**
 * 角色检查中间件
 * @desc 检查用户是否拥有指定角色，未授权则返回403响应。
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><EMAIL>
 * @date 2025-05-16
 */

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use App\Traits\ApiResponseTrait;

class CheckRole
{
    use ApiResponseTrait;

    /**
     * 处理传入的请求
     *
     * 检查用户是否拥有指定角色，未授权则返回403响应
     *
     * @param  Request  $request 请求对象
     * @param  Closure  $next 下一个中间件
     * @param  string  $role 需要检查的角色名称
     * @return mixed
     */
    public function handle($request, Closure $next, $role)
    {
        if (!auth()->check()) {
            return $this->respondUnauthorized(trans('auth.unauthenticated'));
        }

        if (!auth()->user()->hasRole($role, 'web')) {
            return $this->respondForbidden(trans('auth.unauthorized'));
        }

        return $next($request);
    }
}
