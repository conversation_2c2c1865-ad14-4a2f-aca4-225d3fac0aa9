<?php

/**
 * 基础JSON资源类
 * @desc 为所有单个资源提供统一的API响应结构，包含code, message, 和 data字段。
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><EMAIL>
 * @date 2025-05-15
 * @todo 考虑是否需要支持自定义code和message。
 */

namespace App\Http\Resources\Base;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Http\JsonResponse;

class BaseJsonResource extends JsonResource
{
    /**
     * 自定义消息
     *
     * @var string|null
     */
    protected $message = null;

    /**
     * 自定义状态码
     *
     * @var int
     */
    protected $code = 0;

    /**
     * 设置响应消息
     *
     * @param  string  $message
     * @return $this
     */
    public function withMessage(string $message)
    {
        $this->message = $message;
        return $this;
    }

    /**
     * 设置响应状态码
     *
     * @param  int  $code
     * @return $this
     */
    public function withCode(int $code)
    {
        $this->code = $code;
        return $this;
    }

    /**
     * 自定义响应
     *
     * @param  Request  $request
     * @param  JsonResponse  $response
     * @return void
     */
    public function withResponse($request, $response)
    {
        $originalData = $response->getData(true);

        // 使用 ResponseService 的格式，但保持原始的 HTTP 状态码
        $statusCode = $response->getStatusCode();

        // 直接使用原始数据，不再嵌套
        $formattedResponse = [
            'code' => $this->code,
            'message' => $this->message ?? trans('response.success'),
            'data' => $originalData['data'] ?? $originalData
        ];

        // 保留原始状态码但使用自定义的数据格式
        $response->setData($formattedResponse);
        $response->setStatusCode($statusCode);
    }
}
