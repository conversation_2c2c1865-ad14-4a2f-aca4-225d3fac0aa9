<?php

/**
 * 版本白名单模型
 * @desc 版本白名单模型
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2025/05/28
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Models\HitBug;

use Illuminate\Database\Eloquent\Model;

class VersionWhiteList extends Model
{
    /**
     * 数据库连接
     *
     * @var string
     */
    protected $connection = 'hitbug';

    /**
     * 表名
     *
     * @var string
     */
    protected $table = 'version_white_list';

    /**
     * 可填充字段
     *
     * @var array
     */
    protected $fillable = [
        'developer_app_id',
        'app_version'
    ];
}
