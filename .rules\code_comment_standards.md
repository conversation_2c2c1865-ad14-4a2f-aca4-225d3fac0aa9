---
description: Laravel代码注释规范
globs: "**/*.php"
alwaysApply: true
---
---
# Rule: Laravel代码注释规范 (Laravel Code Comment Standards)
# Description: 定义Laravel项目中代码注释的标准格式和内容要求，确保代码的可读性和可维护性。
# Trigger: 当创建新文件或修改现有文件时。
# AppliesTo: 项目中的所有PHP文件。
---

## 概述

本规范旨在确保Laravel项目中的所有代码都有清晰、一致的注释，提高代码的可读性和可维护性。良好的注释可以帮助开发人员理解代码的功能、用途和实现细节，减少维护成本。

## 核心原则

1. **清晰性**: 注释应清晰描述代码的功能和用途
2. **一致性**: 注释格式应保持一致
3. **必要性**: 添加必要的注释，避免过度注释
4. **及时性**: 代码变更时同步更新注释
5. **可读性**: 注释应易于阅读和理解

## 文件头部注释规范

每个PHP文件都应在文件顶部添加注释块，说明文件的功能、作者、日期等信息：

```php
/**
 * [功能说明]
 * @desc [详细功能说明]
 * <AUTHOR> <EMAIL>
 * @date YYYY/MM/DD
 * @todo [待办事项]
 */
```

示例：

```php
/**
 * 用户控制器
 * @desc 处理用户的创建、查询、更新和删除等操作
 * <AUTHOR> <EMAIL>
 * @date 2025/05/15
 * @todo 添加用户导出功能
 */
```

## 类注释规范

每个类都应有注释块，说明类的功能和用途：

```php
/**
 * 类名
 * 
 * 类的详细描述，说明类的功能、用途和使用方法
 * 
 * @package 命名空间
 */
```

示例：

```php
/**
 * 用户模型
 * 
 * 表示系统中的用户实体，包含用户的基本信息和相关操作
 * 
 * @package App\Models\User
 */
class User extends Authenticatable
{
    // 类内容
}
```

## 方法注释规范

每个公共方法和重要的私有方法都应有注释块，说明方法的功能、参数和返回值：

```php
/**
 * 方法名称
 *
 * 方法的详细描述，说明方法的功能和用途
 *
 * @param 类型 $参数名 参数说明
 * @return 类型 返回值说明
 * @throws 异常类型 抛出异常的条件
 */
```

示例：

```php
/**
 * 获取用户列表
 *
 * 支持分页、搜索和排序功能
 *
 * @param Request $request 请求对象，包含查询参数
 * @return UserCollection 用户集合资源
 */
public function index(Request $request)
{
    // 方法内容
}
```

## 属性注释规范

类的属性应有注释，说明属性的用途和类型：

```php
/**
 * 属性说明
 *
 * @var 类型
 */
```

示例：

```php
/**
 * 可批量赋值的属性
 *
 * @var array<int, string>
 */
protected $fillable = [
    'username',
    'nickname',
    'is_active',
];
```

## 常量注释规范

常量应有注释，说明常量的用途和值的含义：

```php
/**
 * 常量说明
 */
```

示例：

```php
/**
 * 用户状态：激活
 */
const STATUS_ACTIVE = 1;

/**
 * 用户状态：禁用
 */
const STATUS_INACTIVE = 0;
```

## 代码块注释规范

复杂的代码块应有注释，说明代码块的功能和实现逻辑：

```php
// 注释说明代码块的功能
```

示例：

```php
// 筛选
if ($request->has('keyword')) {
    $searchTerm = $request->input('keyword');
    $query->where(function ($q) use ($searchTerm) {
        $q->where('username', 'like', "%{$searchTerm}%")
            ->orWhere('nickname', 'like', "%{$searchTerm}%");
    });
}
```

## 行内注释规范

对于需要特别说明的代码行，可以添加行内注释：

```php
$variable = value; // 注释说明
```

示例：

```php
$perPage = $request->input('per_page', 15); // 默认每页15条记录
```

## 注释内容规范

### 功能说明

注释应清晰描述代码的功能和用途，回答"这段代码做什么"的问题：

```php
/**
 * 验证用户输入并创建新用户
 */
```

### 实现细节

对于复杂的实现，注释应说明实现的思路和关键步骤：

```php
/**
 * 根据用户权限过滤查询结果
 * 
 * 1. 获取当前用户的角色
 * 2. 检查角色是否有管理员权限
 * 3. 如果不是管理员，则只返回用户自己创建的记录
 */
```

### 参数说明

方法参数的注释应说明参数的类型、用途和约束：

```php
/**
 * @param string $username 用户名，长度在3-20个字符之间
 * @param bool $isActive 是否激活账户，默认为true
 */
```

### 返回值说明

方法返回值的注释应说明返回值的类型和含义：

```php
/**
 * @return UserResource 用户资源对象，包含用户的基本信息
 * @return null 如果用户不存在
 */
```

### 异常说明

方法可能抛出的异常应在注释中说明：

```php
/**
 * @throws AuthorizationException 如果用户没有权限
 * @throws ValidationException 如果输入验证失败
 */
```

## 特殊注释标记

### TODO标记

使用TODO标记标识待完成的工作：

```php
// TODO: 实现用户导出功能
```

### FIXME标记

使用FIXME标记标识需要修复的问题：

```php
// FIXME: 修复用户名重复检查的bug
```

### NOTE标记

使用NOTE标记添加重要说明：

```php
// NOTE: 此方法依赖第三方API，可能需要处理网络异常
```

## 最佳实践

1. **保持简洁**: 注释应简洁明了，避免冗余
2. **避免显而易见**: 不要注释显而易见的代码
3. **解释为什么**: 除了说明代码做什么，还应说明为什么这样做
4. **保持更新**: 代码变更时同步更新注释
5. **使用完整句子**: 注释应使用完整的句子，以大写字母开头，以句号结尾

## 示例

### 控制器方法示例

```php
/**
 * 创建新用户
 *
 * 根据请求数据创建新的用户记录，并返回成功消息
 *
 * @param StoreUserRequest $request 经过验证的用户创建请求
 * @return \Illuminate\Http\Response 包含成功信息的响应
 */
public function store(StoreUserRequest $request)
{
    // 获取验证后的数据
    $validatedData = $request->validated();
    
    // 创建用户
    User::query()->create($validatedData);
    
    // 返回成功响应
    return $this->respondCreated(null, trans('response.user_created'));
}
```

### 模型属性示例

```php
/**
 * 数据库连接名称
 *
 * @var string
 */
protected $connection = 'new_package_tool';

/**
 * 可批量赋值的属性
 *
 * @var array<int, string>
 */
protected $fillable = [
    'username',    // 用户名，从第三方Token获取，唯一业务主键
    'nickname',    // 用户昵称/显示名称
    'is_active',   // 账户是否激活状态
    'third_party_user_id', // 第三方系统中的用户唯一ID
    'last_login_at',       // 最后登录时间
];
```

## 触发条件与适用范围

- **触发时机**:
  - 当创建新的PHP文件时
  - 当添加新的类、方法或属性时
  - 当修改现有代码时
  - 当进行代码审查时

- **适用范围**:
  - 项目中的所有PHP文件
  - 特别是控制器、模型、服务类等核心代码
