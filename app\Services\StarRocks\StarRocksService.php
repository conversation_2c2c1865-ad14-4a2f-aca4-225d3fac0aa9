<?php

/**
 * StarRocks操作方法
 * @desc StarRocks操作方法
 * <AUTHOR> chen<PERSON><PERSON><EMAIL>
 * @date 2023/10/24
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Services\StarRocks;

use Illuminate\Support\Facades\Log;

class StarRocksService
{
    //连接句柄
    private $conn;

    /**
     * 构造函数
     *
     * @param $db 数据库名称
     */
    public function __construct($db)
    {
        $starRocks = config('starRocks');
        $conn = new \mysqli($starRocks['starRocks_host'], $starRocks['starRocks_username'], $starRocks['starRocks_password'], $starRocks[$db], $starRocks['starRocks_port']);
        if ($conn->connect_error) {
            Log::error("StarRocks数据库连接失败 - 错误信息: {$conn->connect_error}, 主机: {$starRocks['starRocks_host']}, 端口: {$starRocks['starRocks_port']}, 数据库: {$starRocks[$db]}");
            die("starRocks连接失败: " . $conn->connect_error);
        }
        $this->conn = $conn;
    }

    /**
     * 查询
     * @param $sql
     * @return array
     * @throws \Exception
     */
    public function query($sql)
    {
        if (!$this->conn) {
            throw new \Exception('starRocks连接失败');
        }

        $result = $this->conn->query($sql);

        // 检查查询是否成功执行
        if ($result === false) {
            $error = $this->conn->error;
            $errno = $this->conn->errno;
            Log::error("StarRocks查询失败 - 错误码: {$errno}, 错误信息: {$error}, SQL语句: {$sql}");
            throw new \Exception("StarRocks查询失败: [{$errno}] {$error}");
        }

        $res = $result->fetch_all(MYSQLI_ASSOC);
        $result->free_result();
        return $res;
    }

    /**
     * 执行
     * @param $sql
     * @return bool|\mysqli_result
     * @throws \Exception
     */
    public function execute($sql)
    {
        if (!$this->conn) {
            throw new \Exception('starRocks连接失败');
        }

        $result = $this->conn->query($sql);

        // 检查查询是否成功执行
        if ($result === false) {
            $error = $this->conn->error;
            $errno = $this->conn->errno;
            Log::error("StarRocks执行失败 - 错误码: {$errno}, 错误信息: {$error}, SQL语句: {$sql}");
            throw new \Exception("StarRocks执行失败: [{$errno}] {$error}");
        }

        return $result;
    }

    /**
     * 析构函数
     */
    public function __destruct()
    {
        $this->close();
    }

    /**
     * 关闭连接
     */
    public function close()
    {
        if ($this->conn) {
            $this->conn->close();
        }
    }
}
