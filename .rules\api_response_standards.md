---
description: Laravel API响应规范
globs: "**/*.php"
alwaysApply: true
---
---
# Rule: Laravel API响应规范 (Laravel API Response Standards)
# Description: 定义Laravel项目中API响应的标准格式，确保所有API端点返回一致的响应结构。
# Trigger: 当开发新的API端点或修改现有API响应时。
# AppliesTo: 控制器、资源类、中间件等处理API响应的文件。
---

## 概述

本规范旨在确保Laravel项目中的所有API端点返回一致的响应格式，提高API的可用性和可维护性。统一的响应格式使前端开发更加简单，错误处理更加一致，并提高了API的可读性和可测试性。

## 核心原则

1. **统一结构**: 所有API响应使用相同的顶层结构
2. **明确状态**: 使用明确的状态码和消息传达操作结果
3. **国际化支持**: 所有响应消息支持多语言
4. **一致性**: 成功和错误响应使用一致的格式
5. **简洁性**: 响应结构简洁明了，避免不必要的嵌套

## 标准响应格式

### 基本结构

所有API响应必须使用以下JSON格式：

```json
{
    "code": 0,           // 业务状态码，0表示成功，其他值表示错误
    "message": "操作成功", // 响应消息，支持国际化
    "data": {            // 响应数据，可以是对象、数组或null
        // 具体数据
    }
}
```

### 字段说明

1. **code** (必须):
   - 类型: 整数
   - 描述: 业务状态码，0表示成功，其他值表示特定错误
   - 示例: 0(成功), 400(请求错误), 401(未授权), 403(禁止访问), 404(资源不存在), 422(验证错误), 500(服务器错误)

2. **message** (必须):
   - 类型: 字符串
   - 描述: 操作结果的描述信息，应使用国际化翻译
   - 示例: "操作成功", "用户创建成功", "资源不存在", "验证失败"

3. **data** (必须):
   - 类型: 对象、数组或null
   - 描述: 响应的主体数据
   - 说明: 即使没有数据返回，也应包含此字段并设为null

## 响应类型规范

### 成功响应

#### 单个资源响应

返回单个资源对象：

```json
{
    "code": 0,
    "message": "操作成功",
    "data": {
        "id": 1,
        "username": "john_doe",
        "nickname": "John Doe",
        "is_active": true,
        "created_at": "2025-05-15 10:30:00",
        "updated_at": "2025-05-15 10:30:00"
    }
}
```

#### 列表资源响应

返回资源列表，包含分页信息：

```json
{
    "code": 0,
    "message": "操作成功",
    "data": {
        "list": [  // 使用list而非data
            {
                "id": 1,
                "username": "john_doe",
                // 其他字段
            },
            // 更多列表项
        ],
        "meta": {  // 分页元数据，不包含links和path
            "current_page": 1,
            "from": 1,
            "last_page": 5,
            "per_page": 15,
            "to": 15,
            "total": 75
        }
    }
}
```

#### 创建成功响应

创建资源成功后的响应：

```json
{
    "code": 0,
    "message": "用户创建成功",
    "data": null  // 通常不返回创建的资源详情
}
```

#### 更新成功响应

更新资源成功后的响应：

```json
{
    "code": 0,
    "message": "用户更新成功",
    "data": {  // 可以返回更新后的资源
        "id": 1,
        "username": "john_doe",
        // 更新后的字段
    }
}
```

#### 删除成功响应

删除资源成功后的响应：

```json
{
    "code": 0,
    "message": "用户删除成功",
    "data": null
}
```

### 错误响应

#### 验证错误响应

表单验证失败的响应：

```json
{
    "code": 422,
    "message": "验证失败：用户名已被占用",  // 第一条验证错误消息
    "data": null  // 不返回详细的验证错误信息
}
```

#### 资源不存在响应

请求的资源不存在的响应：

```json
{
    "code": 404,
    "message": "用户不存在",
    "data": null
}
```

#### 未授权响应

用户未登录或token无效的响应：

```json
{
    "code": 401,
    "message": "未授权访问",
    "data": null
}
```

#### 禁止访问响应

用户无权执行操作的响应：

```json
{
    "code": 403,
    "message": "禁止访问",
    "data": null
}
```

#### 服务器错误响应

服务器内部错误的响应：

```json
{
    "code": 500,
    "message": "服务器错误",
    "data": null
}
```

## 实现指南

### 使用ResponseService

项目提供了`App\Services\Response\ResponseService`服务类，用于生成标准格式的响应：

```php
// 成功响应
return ResponseService::success($data, $message, $code, $httpStatusCode);

// 错误响应
return ResponseService::error($message, $code, $data, $httpStatusCode);

// 验证错误响应
return ResponseService::validationError($message, $data);

// 未授权响应
return ResponseService::unauthorized($message);

// 禁止访问响应
return ResponseService::forbidden($message);

// 资源不存在响应
return ResponseService::notFound($message);

// 服务器错误响应
return ResponseService::serverError($message);
```

### 使用ApiResponseTrait

控制器可以使用`App\Traits\ApiResponseTrait`特性，提供便捷的响应方法：

```php
// 成功响应
return $this->respondSuccess($data, $message, $code, $httpStatusCode);

// 创建成功响应
return $this->respondCreated($data, $message);

// 错误响应
return $this->respondError($message, $code, $data, $httpStatusCode);

// 验证错误响应
return $this->respondValidationError($message, $data);

// 未授权响应
return $this->respondUnauthorized($message);

// 禁止访问响应
return $this->respondForbidden($message);

// 资源不存在响应
return $this->respondNotFound($message);

// 服务器错误响应
return $this->respondServerError($message);
```

### 使用资源类

使用资源类格式化响应数据：

```php
// 单个资源
return new UserResource($user);

// 资源集合
return new UserCollection($users);
```

## 国际化支持

所有响应消息都应使用国际化翻译：

```php
// 使用翻译文件中的消息
return $this->respondSuccess(null, trans('response.user_created'));
```

翻译文件应放在`resources/lang/{locale}/response.php`中：

```php
// resources/lang/zh_CN/response.php
return [
    'success' => '操作成功',
    'user_created' => '用户创建成功',
    'user_updated' => '用户更新成功',
    'user_deleted' => '用户删除成功',
    // 其他消息
];
```

## 最佳实践

1. **使用适当的HTTP状态码**: 响应的HTTP状态码应与业务状态码一致
2. **提供有意义的消息**: 响应消息应清晰描述操作结果
3. **避免敏感信息**: 响应中不应包含敏感信息
4. **保持一致性**: 所有API端点应使用相同的响应格式
5. **使用国际化**: 所有面向用户的消息都应支持国际化

## 触发条件与适用范围

- **触发时机**:
  - 当开发新的API端点时
  - 当修改现有API响应时
  - 当进行代码审查时

- **适用范围**:
  - 控制器
  - 资源类
  - 中间件
  - 异常处理器
  - 其他处理API响应的文件
