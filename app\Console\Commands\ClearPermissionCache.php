<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Spatie\Permission\PermissionRegistrar;

class ClearPermissionCache extends Command
{
    /**
     * 命令名称
     *
     * @var string
     */
    protected $signature = 'permission:clear-cache';

    /**
     * 命令描述
     *
     * @var string
     */
    protected $description = '清除权限缓存';

    /**
     * 执行命令
     *
     * @return int
     */
    public function handle()
    {
        app(PermissionRegistrar::class)->forgetCachedPermissions();

        $this->info('权限缓存已清除!');

        return 0;
    }
}
