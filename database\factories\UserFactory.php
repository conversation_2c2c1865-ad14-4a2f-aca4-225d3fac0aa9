<?php

namespace Database\Factories;

use App\Models\User\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class UserFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = User::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'username' => $this->faker->unique()->userName(),
            'nickname' => $this->faker->name(),
            'is_active' => true,
            'third_party_user_id' => $this->faker->unique()->uuid(),
            'last_login_at' => now(),
        ];
    }
}
