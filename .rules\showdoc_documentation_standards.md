---
description: Laravel API文档规范
globs: "**/*.md"
alwaysApply: true
---
---
# Rule: Laravel API文档规范 (Laravel API Documentation Standards)
# Description: 定义Laravel项目中API文档的标准格式和组织方式，确保文档的一致性、可读性和可维护性。
# Trigger: 当创建或更新API文档时。
# AppliesTo: doc/showdoc目录下的所有Markdown文档文件。
---

## 概述

本规范旨在确保Laravel项目中的API文档遵循一致的格式和组织方式，提高文档的可读性和可维护性。良好的API文档是前后端协作的基础，能够减少沟通成本，提高开发效率。

## 核心原则

1. **一致性**: 所有文档遵循统一的格式和结构
2. **完整性**: 文档应包含API的所有必要信息
3. **模块化**: 按功能模块组织文档，避免单个文档过长
4. **可读性**: 文档应易于阅读和理解
5. **可维护性**: 文档结构应便于更新和维护

## 文档组织规范

### 目录结构

API文档应放在`doc/showdoc/`目录下，按功能模块组织：

```
doc/
└── showdoc/
    ├── 用户管理模块.md                  # 模块索引文档
    ├── 用户管理模块-认证接口.md          # 子功能文档
    ├── 用户管理模块-用户CRUD.md          # 子功能文档
    ├── 用户管理模块-用户角色管理.md       # 子功能文档
    ├── 权限管理模块.md                  # 模块索引文档
    ├── 权限管理模块-角色管理.md          # 子功能文档
    ├── 权限管理模块-权限与权限组.md       # 子功能文档
    └── 权限管理模块-菜单管理.md          # 子功能文档
```

### 文档拆分原则

1. **按功能模块拆分**: 每个主要功能模块应有一个索引文档和多个子功能文档
2. **控制文档长度**: 单个文档不应过长，建议不超过500行
3. **相关性分组**: 相关的API应放在同一个文档中
4. **避免重复**: 共用的信息应放在索引文档中，避免在子文档中重复

## 文档格式规范

### 文档头部

每个文档都应包含以下头部信息：

```markdown
# 模块名称 API 文档

> 作者：姓名
>
> 日期：YYYY-MM-DD
>
> 版本：X.Y.Z
```

### 文档结构

每个文档应包含以下部分：

1. **简介**: 简要说明文档的内容和用途
2. **API基础信息**: 包含基础URL、环境信息、认证方式、响应格式等
3. **API接口列表**: 按功能分组的API接口列表
4. **错误响应**: 常见错误响应的格式和示例

### 索引文档结构

索引文档应包含以下部分：

1. **简介**: 简要说明模块的功能和用途
2. **API基础信息**: 包含基础URL、环境信息、认证方式、响应格式等
3. **文档导航**: 链接到各个子功能文档，并简要说明每个子文档的内容
4. **错误响应**: 常见错误响应的格式和示例

### API基础信息部分

API基础信息部分应包含以下内容：

```markdown
## API 基础信息

- **基础URL**: `/api`
- **环境**:
  - **测试环境**: `https://test-api.example.com/api`
  - **正式环境**: `https://api.example.com/api`
- **认证方式**: Bearer Token (Sanctum)
- **响应格式**: JSON
- **统一响应结构**:
  ```json
  {
    "code": 0,       // 业务状态码，0表示成功，其他值表示错误
    "message": "操作成功", // 业务消息，支持国际化
    "data": { ... }    // 业务数据，可能为对象、数组或null
  }
  ```
```

## API接口文档规范

### 接口描述

每个API接口的文档应包含以下部分：

1. **接口标题**: 使用三级标题（###）
2. **接口描述**: 简要说明接口的功能和用途
3. **接口URL**: 完整的接口路径
4. **请求方式**: GET、POST、PUT、DELETE等
5. **是否需要认证**: 是/否
6. **权限要求**: 如果需要特定权限，应说明权限名称

```markdown
### 接口名称

> 接口功能描述

- **接口URL**: `/path/to/endpoint`
- **请求方式**: METHOD
- **是否需要认证**: 是/否
- **权限要求**: `permission.name`（如果需要）
```

### 请求参数

请求参数应使用表格形式说明，包含参数名、类型、是否必填和描述：

```markdown
#### 请求参数

| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| param1 | string | 是 | 参数1的描述 |
| param2 | integer | 否 | 参数2的描述，默认值为10 |
```

对于路径参数，应单独列出：

```markdown
#### 路径参数

| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| id | integer | 是 | 资源ID |
```

### 响应参数

响应参数应使用表格形式说明，包含参数名、类型和描述：

```markdown
#### 响应参数

| 参数名 | 类型 | 描述 |
| ------ | ---- | ---- |
| code | integer | 业务状态码，0表示成功 |
| message | string | 业务消息，支持国际化 |
| data | object | 业务数据 |
| data.id | integer | 资源ID |
| data.name | string | 资源名称 |
```

对于嵌套对象和数组，应使用点表示法或中括号表示法：

- 对象属性：`data.property`
- 数组元素：`data.list[]`
- 数组元素的属性：`data.list[].property`

### 请求示例

对于POST、PUT等包含请求体的方法，应提供JSON格式的请求示例：

```markdown
#### 请求示例

```json
{
  "param1": "value1",
  "param2": 10
}
```
```

### 响应示例

每个接口都应提供JSON格式的响应示例：

```markdown
#### 响应示例

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "id": 1,
    "name": "示例名称",
    "created_at": "2023-05-16 10:00:00",
    "updated_at": "2023-05-16 10:00:00"
  }
}
```
```

## 错误响应规范

每个文档都应在末尾包含错误响应部分，说明常见的错误响应格式：

```markdown
## 错误响应

### 权限不足

```json
{
  "code": 403,
  "message": "权限不足",
  "data": null
}
```

### 资源不存在

```json
{
  "code": 404,
  "message": "资源不存在",
  "data": null
}
```

### 参数验证失败

```json
{
  "code": 422,
  "message": "参数验证失败",
  "data": null
}
```
```

## 最佳实践

1. **保持更新**: 代码变更时同步更新文档
2. **完整示例**: 提供完整的请求和响应示例
3. **详细说明**: 对复杂参数提供详细说明
4. **一致性**: 保持所有文档的格式一致
5. **避免重复**: 使用索引文档避免信息重复
6. **链接相关**: 在相关API之间添加链接，便于导航

## 触发条件与适用范围

- **触发时机**:
  - 当创建新的API文档时
  - 当更新现有API文档时
  - 当进行文档审查时

- **适用范围**:
  - `doc/showdoc/`目录下的所有Markdown文档文件
