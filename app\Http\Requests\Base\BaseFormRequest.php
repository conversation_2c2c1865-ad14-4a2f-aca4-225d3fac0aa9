<?php

/**
 * 基础表单请求类
 * @desc 所有API表单请求类应继承此类，以实现统一的验证失败JSON响应格式。
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><EMAIL>
 * @date 2025-05-15
 * @todo 考虑将错误码和消息配置化
 */

namespace App\Http\Requests\Base;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;
use App\Traits\ApiResponseTrait;

abstract class BaseFormRequest extends FormRequest
{
    use ApiResponseTrait;

    /**
     * 确定用户是否有权提出此请求。
     * 子类可以覆盖此方法以实现特定的授权逻辑。
     *
     * @return bool
     */
    public function authorize()
    {
        // 默认允许所有请求，子类应根据需要覆盖此方法
        return true;
    }

    /**
     * 获取适用于请求的验证规则。
     * 子类必须覆盖此方法以提供特定的验证规则。
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        // 子类必须覆盖此方法
        return [];
    }

    /**
     * Handle a failed validation attempt.
     *
     * @param  \Illuminate\Contracts\Validation\Validator  $validator
     * @return void
     *
     * @throws \Illuminate\Http\Exceptions\HttpResponseException
     */
    protected function failedValidation(Validator $validator)
    {
        // 获取第一条错误信息
        $firstErrorMessage = $validator->errors()->first() ?? trans('validation.failed');

        throw new HttpResponseException(
            $this->respondValidationError($firstErrorMessage, null)
        );
    }
}
