<?php

/**
 * 列表资源集合基类
 * @desc 为所有列表类型的资源集合提供统一的API响应结构，包含list和meta，不包含links和path信息。
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><EMAIL>
 * @date 2025-05-16
 * @todo 暂无特定待办事项。
 */

namespace App\Http\Resources\Base;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class ListResourceCollection extends BaseResourceCollection
{
    /**
     * 将资源集合转换为数组。
     *
     * @param  Request  $request
     * @return array<string|int, mixed>
     */
    public function toArray($request): array
    {
        // 直接返回父类的 toArray 方法结果
        // 我们将在 withResponse 方法中处理最终的响应结构
        return parent::toArray($request);
    }

    /**
     * 自定义响应中包含的数据。
     *
     * @param  Request  $request
     * @param  JsonResponse  $response
     * @return void
     */
    public function withResponse($request, $response)
    {
        // 获取原始响应数据
        $responseData = $response->getData(true);

        // 构建新的响应结构
        $data = [
            'list' => $responseData['data'] ?? [],
            'meta' => isset($responseData['meta']) ? array_diff_key($responseData['meta'], ['links' => 1, 'path' => 1]) : []
        ];

        // 使用统一的响应格式
        $formattedResponse = [
            'code' => $this->code,
            'message' => $this->message ?? trans('response.success'),
            'data' => $data
        ];

        // 设置响应数据
        $response->setData($formattedResponse);
    }
}
