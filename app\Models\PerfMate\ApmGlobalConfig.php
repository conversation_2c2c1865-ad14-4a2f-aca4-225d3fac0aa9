<?php

/**
 * 性能监控全局配置
 * @desc 性能监控全局配置
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2025/05/28
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Models\PerfMate;

use Illuminate\Database\Eloquent\Model;

class ApmGlobalConfig extends Model
{
    /**
     * 数据库连接
     *
     * @var string
     */
    public $connection = "perfmate";

    /**
     * 表名
     *
     * @var string
     */
    protected $table = 'apm_global_config';

    /**
     * 主键
     *
     * @var string
     */
    protected $primaryKey = 'developer_app_id';

    /**
     * 获取最小采集时间，单位秒
     *
     * @return float|int
     */
    public function getMinDurationAttribute()
    {
        $config = json_decode($this->config, true) ?? [];
        return $config['min_duration'] ?? 0;
    }
}
