# 大盘模块 API 使用说明

> 作者：陈建权
>
> 日期：2025-06-05
>
> 版本：1.0.0

## 概述

本文档提供大盘模块 API 的详细使用说明，包括接口调用方法和数据解析。该模块提供 SDK 监控数据的综合展示功能，整合版本数据、异常数据、性能数据和 SDK 比率数据，为开发者提供全面的监控大盘。

## 环境要求

- PHP 8.1+
- Laravel 10+
- MySQL 5.7+ / 8.0+
- Redis (用于缓存)
- 腾讯云 CLS 服务
- HitBug 监控服务
- PerfMate 性能监控服务

## API 基础信息

- **基础URL**: `/api`
- **环境**:
  - **正式环境**: `https://sdk-monitor-dashboard.shiyue.com/api`
- **认证方式**: 无需认证
- **响应格式**: JSON
- **统一响应结构**:
  ```json
  {
    "code": 0,       // 业务状态码，0表示成功，其他值表示错误
    "message": "操作成功", // 业务消息，支持国际化
    "data": { ... }    // 业务数据，可能为对象、数组或null
  }
  ```

## API 接口说明

### 获取大盘数据

获取 SDK 监控大盘的综合数据，包括版本分析、异常统计、性能指标和关键比率数据。

- **URL**: `/api/dashboard`
- **方法**: `GET`
- **认证**: 不需要

#### 请求参数

| 参数名           | 类型    | 必填 | 默认值 | 描述                                    |
| :--------------- | :------ | :--- | :----- | :-------------------------------------- |
| developer_app_id | integer | 是   | 无     | 开发者应用ID，必须大于0                 |
| start_time       | string  | 是   | 无     | 开始时间，格式：Y-m-d H:i:s             |
| end_time         | string  | 是   | 无     | 结束时间，格式：Y-m-d H:i:s，必须晚于开始时间 |

#### 请求示例

```bash
curl -X GET "https://sdk-monitor-dashboard.shiyue.com/api/dashboard" \
  -H "Content-Type: application/json" \
  -G \
  -d "developer_app_id=6" \
  -d "start_time=2025-05-29 00:00:00" \
  -d "end_time=2025-05-30 00:00:00"
```

#### 成功响应 (200)

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "inner_version": [
      {
        "version": "202410261816",
        "app_launch_error_rate": "0.00",
        "crash_rate": "0",
        "error_rate": "0",
        "smoothness": "0"
      },
      {
        "version": "2025052313",
        "app_launch_error_rate": "75.00",
        "crash_rate": "0",
        "error_rate": "0",
        "smoothness": "0"
      }
    ],
    "app_version": [
      {
        "version": "1.1.1",
        "app_launch_error_rate": "0.00",
        "crash_rate": "0",
        "error_rate": "0",
        "smoothness": "0"
      }
    ],
    "hit_bug_data": {
      "crash": "0.00",
      "error": "0.00",
      "start_count": "0",
      "crash_count": "0",
      "error_count": "0",
      "start_dev_num": "0",
      "crash_dev_num": "0",
      "error_dev_num": "0"
    },
    "perf_mate_data": {
      "smoothness": "0",
      "avg_memory": "0",
      "avg_network_traffic": "0",
      "avg_network_delay": "0",
      "avg_battery_power": "0",
      "avg_battery_temp": "0",
      "avg_fps_power": "0"
    },
    "sdk_data": {
      "app_launch_error": {
        "event_name": "app_launch_error",
        "count": "6",
        "avg_value": "0.00",
        "sum_value": "0"
      },
      "sdk_init_success": {
        "event_name": "sdk_init_success",
        "count": "15",
        "avg_value": "1.00",
        "sum_value": "14"
      }
    },
    "sdk_rate": {
      "average_startup_time": "0.00",
      "app_launch_error_rate": "100.00",
      "hot_update_time": "3.00",
      "hot_update_error_rate": "100.00",
      "sdk_init_error_rate": "28.57"
    }
  }
}
```

#### 错误响应

**参数验证失败 (422)**
```json
{
  "code": 422,
  "message": "验证失败：开发者应用ID必须是整数",
  "data": null
}
```

**开始时间晚于结束时间 (422)**
```json
{
  "code": 422,
  "message": "验证失败：结束时间必须晚于开始时间",
  "data": null
}
```

**服务器错误 (500)**
```json
{
  "code": 500,
  "message": "服务器内部错误",
  "data": null
}
```

## 响应数据详解

### inner_version 字段说明

内部版本数据数组，每个版本包含以下字段：

| 字段名                | 类型   | 描述                                    |
| :-------------------- | :----- | :-------------------------------------- |
| version               | string | 内部版本号（时间戳格式，如：202410261816） |
| app_launch_error_rate | string | 应用启动错误率（保留两位小数）           |
| crash_rate            | string | 崩溃率                                  |
| error_rate            | string | 错误率                                  |
| smoothness            | string | 卡顿率                                  |

### app_version 字段说明

应用版本数据数组，每个版本包含以下字段：

| 字段名                | 类型   | 描述                                    |
| :-------------------- | :----- | :-------------------------------------- |
| version               | string | 应用版本号（语义化版本，如：1.1.1）      |
| app_launch_error_rate | string | 应用启动错误率（保留两位小数）           |
| crash_rate            | string | 崩溃率                                  |
| error_rate            | string | 错误率                                  |
| smoothness            | string | 卡顿率                                  |

### hit_bug_data 字段说明

异常监控数据对象，包含以下字段：

| 字段名         | 类型   | 描述                          |
| :------------- | :----- | :---------------------------- |
| crash          | string | 崩溃率（保留两位小数）         |
| error          | string | 错误率（保留两位小数）         |
| start_count    | string | 启动次数                      |
| crash_count    | string | 崩溃次数                      |
| error_count    | string | 错误次数                      |
| start_dev_num  | string | 启动设备数                    |
| crash_dev_num  | string | 崩溃设备数                    |
| error_dev_num  | string | 错误设备数                    |

### perf_mate_data 字段说明

性能监控数据对象，包含以下字段：

| 字段名               | 类型   | 描述                                    |
| :------------------- | :----- | :-------------------------------------- |
| smoothness           | string | 卡顿率（保留两位小数）                   |
| avg_memory           | string | 平均内存使用（保留两位小数，单位：MB）    |
| avg_network_traffic  | string | 平均网络流量（保留两位小数，单位：KB）    |
| avg_network_delay    | string | 平均网络延迟（保留两位小数，单位：ms）    |
| avg_battery_power    | string | 平均电池电量（保留两位小数，单位：%）     |
| avg_battery_temp     | string | 平均电池温度（保留两位小数，单位：℃）     |
| avg_fps_power        | string | 每帧功耗（保留两位小数，单位：mW）       |

### sdk_data 字段说明

SDK 事件数据对象，包含各种事件的统计信息。每个事件包含以下字段：

| 字段名     | 类型   | 描述                          |
| :--------- | :----- | :---------------------------- |
| event_name | string | 事件名称                      |
| count      | string | 事件发生次数                  |
| avg_value  | string | 平均值（保留两位小数）         |
| sum_value  | string | 总值                          |

**支持的事件类型包括但不限于：**
- `app_launch_error`: 应用启动错误
- `app_launch_screen_completion`: 应用启动屏幕完成
- `sdk_init_error`: SDK初始化错误
- `sdk_init_success`: SDK初始化成功
- `hot_update_error`: 热更新错误
- `download_error`: 下载错误
- 等其他 SDK 相关事件

### sdk_rate 字段说明

SDK 关键性能指标对象，包含计算后的比率数据：

| 字段名                            | 类型   | 描述                                    |
| :-------------------------------- | :----- | :-------------------------------------- |
| average_startup_time              | string | 平均启动时间（保留两位小数，单位：秒）    |
| app_launch_error_rate             | string | 应用启动错误率（保留两位小数）           |
| hot_update_time                   | string | 热更新时间（保留两位小数，单位：秒）      |
| hot_update_error_rate             | string | 热更新错误率（保留两位小数）             |
| sdk_init_error_rate               | string | SDK初始化错误率（保留两位小数）          |
| sdk_login_error_rate              | string | SDK登录错误率（保留两位小数）            |
| login_to_main_screen_time         | string | 登录到主界面时间（保留两位小数，单位：秒） |
| download_error_rate               | string | 下载错误率（保留两位小数）               |
| network_disconnection_rate        | string | 网络断开率（保留两位小数）               |

## 前端集成示例

### JavaScript/Vue.js 示例

```javascript
// 获取大盘数据
async function getDashboardData(developerAppId, startTime, endTime) {
  try {
    const response = await axios.get('/api/dashboard', {
      params: {
        developer_app_id: developerAppId,
        start_time: startTime,
        end_time: endTime
      }
    });

    if (response.data.code === 0) {
      return response.data.data;
    } else {
      throw new Error(response.data.message);
    }
  } catch (error) {
    console.error('获取大盘数据失败:', error.response?.data?.message || error.message);
    throw error;
  }
}

// 使用示例
getDashboardData(6, '2025-05-29 00:00:00', '2025-05-30 00:00:00')
  .then(data => {
    console.log('内部版本数据:', data.inner_version);
    console.log('应用版本数据:', data.app_version);
    console.log('异常数据:', data.hit_bug_data);
    console.log('性能数据:', data.perf_mate_data);
    console.log('SDK事件数据:', data.sdk_data);
    console.log('SDK比率数据:', data.sdk_rate);
  })
  .catch(error => {
    // 处理错误
  });
```

### React 示例

```javascript
import { useState, useEffect } from 'react';

function DashboardComponent() {
  const [dashboardData, setDashboardData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const fetchDashboardData = async (developerAppId, startTime, endTime) => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch(`/api/dashboard?developer_app_id=${developerAppId}&start_time=${encodeURIComponent(startTime)}&end_time=${encodeURIComponent(endTime)}`);
      const result = await response.json();
      
      if (result.code === 0) {
        setDashboardData(result.data);
      } else {
        setError(result.message);
      }
    } catch (err) {
      setError('网络请求失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDashboardData(6, '2025-05-29 00:00:00', '2025-05-30 00:00:00');
  }, []);

  if (loading) return <div>加载中...</div>;
  if (error) return <div>错误: {error}</div>;
  if (!dashboardData) return <div>暂无数据</div>;

  return (
    <div>
      <h2>SDK 监控大盘</h2>
      
      <div>
        <h3>内部版本数据</h3>
        {dashboardData.inner_version.map((version, index) => (
          <div key={index}>
            版本: {version.version}, 启动错误率: {version.app_launch_error_rate}%
          </div>
        ))}
      </div>
      
      <div>
        <h3>异常统计</h3>
        <p>崩溃率: {dashboardData.hit_bug_data.crash}%</p>
        <p>错误率: {dashboardData.hit_bug_data.error}%</p>
      </div>
      
      <div>
        <h3>关键指标</h3>
        <p>平均启动时间: {dashboardData.sdk_rate.average_startup_time}秒</p>
        <p>SDK初始化错误率: {dashboardData.sdk_rate.sdk_init_error_rate}%</p>
      </div>
    </div>
  );
}
```

## 错误处理与状态码

### 响应格式

所有API响应都遵循统一的格式:

```json
{
  "code": 0,         // 业务状态码
  "message": "操作成功", // 业务消息
  "data": { ... }    // 业务数据，可能为对象、数组或null
}
```

### 常见状态码

| HTTP状态码 | 业务状态码 | 说明                     |
| :--------- | :--------- | :----------------------- |
| 200        | 0          | 请求成功                 |
| 400        | 400        | 请求参数错误             |
| 422        | 422        | 请求参数验证失败         |
| 500        | 500        | 服务器内部错误           |

### 参数验证错误

当请求参数验证失败时，返回422状态码，并在message中返回具体的错误信息：

```json
{
  "code": 422,
  "message": "验证失败：开发者应用ID必须是整数",
  "data": null
}
```

**常见验证错误：**
- 开发者应用ID必须是大于0的整数
- 开始时间格式必须为 Y-m-d H:i:s
- 结束时间格式必须为 Y-m-d H:i:s
- 结束时间必须晚于开始时间

## 性能优化建议

### 缓存机制

系统内置了10分钟的数据缓存机制，相同参数的请求会优先返回缓存数据，提高响应速度。

### 请求优化

1. **合理设置时间范围**: 避免查询过长的时间范围，建议单次查询不超过30天
2. **避免频繁请求**: 利用缓存机制，避免短时间内重复请求相同数据
3. **错误重试**: 实现指数退避的重试机制，避免服务器压力过大

### 数据处理

1. **异步加载**: 对于大量数据，建议使用异步加载方式
2. **分页展示**: 对于版本数据等列表，可以考虑分页展示
3. **数据缓存**: 在前端适当缓存数据，减少不必要的请求

## 最佳实践

1. **参数验证**: 在发送请求前进行客户端参数验证
2. **错误处理**: 实现完善的错误处理机制，为用户提供友好的错误提示
3. **加载状态**: 在数据加载过程中显示加载状态，提升用户体验
4. **数据可视化**: 利用图表库将数据进行可视化展示
5. **实时更新**: 可以考虑定时刷新数据，保持数据的实时性

## 注意事项

1. 所有时间参数必须使用 `Y-m-d H:i:s` 格式
2. 结束时间必须晚于开始时间
3. 开发者应用ID必须是大于0的整数
4. 所有数值字段都会被格式化为字符串类型，数值保留两位小数
5. 响应数据较大，建议在网络条件较差时显示加载提示
6. 系统支持缓存机制，相同参数的请求可能返回缓存数据
7. 内部版本号采用时间戳格式，应用版本号采用语义化版本格式
