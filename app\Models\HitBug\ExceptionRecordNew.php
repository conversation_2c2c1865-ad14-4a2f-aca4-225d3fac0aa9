<?php

/**
 * 对应starRocks数仓中exception_record_new表
 * @desc 对应starRocks数仓中exception_record_new表
 * <AUTHOR> chen<PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2025/05/28
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Models\HitBug;

use App\Models\StarRocks\BaseModel;

class ExceptionRecordNew extends BaseModel
{
    public const TABLE_NAME = 'exception_record_new';

    protected $table = self::TABLE_NAME;

    protected $starRocksConnection = 'starRocks_hitbug_database';
}
