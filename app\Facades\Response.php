<?php

namespace App\Facades;

use Illuminate\Support\Facades\Facade;
use App\Services\Response\ResponseService;

/**
 * @method static \Illuminate\Http\JsonResponse success($data = null, ?string $message = null, int $code = 0, int $httpStatusCode = 200)
 * @method static \Illuminate\Http\JsonResponse error(?string $message = null, int $code = 400, $data = null, int $httpStatusCode = 400)
 * @method static \Illuminate\Http\JsonResponse validationError(?string $message = null, $data = null)
 * @method static \Illuminate\Http\JsonResponse unauthorized(?string $message = null)
 * @method static \Illuminate\Http\JsonResponse forbidden(?string $message = null)
 * @method static \Illuminate\Http\JsonResponse notFound(?string $message = null)
 * @method static \Illuminate\Http\JsonResponse serverError(?string $message = null)
 *
 * @see \App\Services\Response\ResponseService
 */
class Response extends Facade
{
    /**
     * Get the registered name of the component.
     *
     * @return string
     */
    protected static function getFacadeAccessor()
    {
        return ResponseService::class;
    }
}
