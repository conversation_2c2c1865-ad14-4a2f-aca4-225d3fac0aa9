<?php

/**
 * 基础资源集合类
 * @desc 为所有资源集合（包括分页数据）提供统一的API响应结构，包含code, message, 和 data字段。
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><EMAIL>
 * @date 2025-05-15
 * @todo 考虑是否需要支持自定义code和message。
 */

namespace App\Http\Resources\Base;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;
use Illuminate\Http\JsonResponse;
use App\Services\Response\ResponseService;

class BaseResourceCollection extends ResourceCollection
{
    /**
     * 自定义消息
     *
     * @var string|null
     */
    protected $message = null;

    /**
     * 自定义状态码
     *
     * @var int
     */
    protected $code = 0;

    /**
     * 设置响应消息
     *
     * @param  string  $message
     * @return $this
     */
    public function withMessage(string $message)
    {
        $this->message = $message;
        return $this;
    }

    /**
     * 设置响应状态码
     *
     * @param  int  $code
     * @return $this
     */
    public function withCode(int $code)
    {
        $this->code = $code;
        return $this;
    }

    /**
     * 自定义响应中包含的数据。
     *
     * @param  Request  $request
     * @param  JsonResponse  $response
     * @return void
     */
    public function withResponse($request, $response)
    {
        // 获取原始响应数据
        $responseData = $response->getData(true);

        // 使用 ResponseService 的格式，但保持原始的 HTTP 状态码
        $statusCode = $response->getStatusCode();

        // 直接使用响应数据，不需要嵌套的data.data结构
        $formattedResponse = ResponseService::success($responseData, $this->message, $this->code);

        // 保留原始状态码但使用 ResponseService 的数据格式
        $response->setData($formattedResponse->getData(true));
        $response->setStatusCode($statusCode);
    }

    /**
     * 将资源集合转换为数组。
     *
     * @param  Request  $request
     * @return array<string|int, mixed>
     */
    public function toArray($request): array
    {
        // 此方法主要确保集合中的每个项目都得到正确转换。
        // withResponse 方法将处理最终的 code/message/data 包装。
        return parent::toArray($request);
    }
}
