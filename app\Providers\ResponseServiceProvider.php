<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Http\JsonResponse;
use App\Services\Response\ResponseService;

class ResponseServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     *
     * @return void
     */
    public function register()
    {
        //
    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot()
    {
        // 扩展 Response 宏，添加统一的 API 响应格式
        JsonResponse::macro('success', function ($data = null, ?string $message = null, int $code = 0, int $httpStatusCode = 200) {
            return ResponseService::success($data, $message, $code, $httpStatusCode);
        });

        JsonResponse::macro('error', function (?string $message = null, int $code = 400, $data = null, int $httpStatusCode = 400) {
            return ResponseService::error($message, $code, $data, $httpStatusCode);
        });

        JsonResponse::macro('validationError', function (?string $message = null, $data = null) {
            return ResponseService::validationError($message, $data);
        });

        JsonResponse::macro('unauthorized', function (?string $message = null) {
            return ResponseService::unauthorized($message);
        });

        JsonResponse::macro('forbidden', function (?string $message = null) {
            return ResponseService::forbidden($message);
        });

        JsonResponse::macro('notFound', function (?string $message = null) {
            return ResponseService::notFound($message);
        });

        JsonResponse::macro('serverError', function (?string $message = null) {
            return ResponseService::serverError($message);
        });
    }
}
